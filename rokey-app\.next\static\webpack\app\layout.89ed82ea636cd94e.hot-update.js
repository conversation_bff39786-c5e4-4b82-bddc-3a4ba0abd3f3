"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"db65314a1aff\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImRiNjUzMTRhMWFmZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,CreditCardIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,CreditCardIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,CreditCardIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,CreditCardIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,CreditCardIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,CreditCardIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,CreditCardIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _GlobalSearch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./GlobalSearch */ \"(app-pages-browser)/./src/components/GlobalSearch.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Navbar() {\n    var _user_user_metadata, _user_user_metadata_full_name, _user_user_metadata1, _user_user_metadata_last_name_charAt, _user_user_metadata_last_name, _user_user_metadata2, _firstName_charAt;\n    _s();\n    const { isCollapsed, isHovered, toggleSidebar } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_3__.useSidebar)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const { user, subscriptionStatus } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__.useSubscription)();\n    const [isSettingsOpen, setIsSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isSearchOpen, setIsSearchOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDesktop, setIsDesktop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_6__.createSupabaseBrowserClient)();\n    // Track if we're on desktop (lg breakpoint and above)\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const checkIsDesktop = {\n                \"Navbar.useEffect.checkIsDesktop\": ()=>{\n                    setIsDesktop(window.innerWidth >= 1024); // lg breakpoint\n                }\n            }[\"Navbar.useEffect.checkIsDesktop\"];\n            // Check on mount\n            checkIsDesktop();\n            // Listen for resize events\n            window.addEventListener('resize', checkIsDesktop);\n            return ({\n                \"Navbar.useEffect\": ()=>window.removeEventListener('resize', checkIsDesktop)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    // Get user display info\n    const firstName = (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.first_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : (_user_user_metadata_full_name = _user_user_metadata1.full_name) === null || _user_user_metadata_full_name === void 0 ? void 0 : _user_user_metadata_full_name.split(' ')[0]) || 'User';\n    const initials = firstName.charAt(0).toUpperCase() + ((user === null || user === void 0 ? void 0 : (_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : (_user_user_metadata_last_name = _user_user_metadata2.last_name) === null || _user_user_metadata_last_name === void 0 ? void 0 : (_user_user_metadata_last_name_charAt = _user_user_metadata_last_name.charAt(0)) === null || _user_user_metadata_last_name_charAt === void 0 ? void 0 : _user_user_metadata_last_name_charAt.toUpperCase()) || ((_firstName_charAt = firstName.charAt(1)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || 'U');\n    // Get simple breadcrumb info from pathname\n    const getBreadcrumb = (path)=>{\n        switch(path){\n            case '/dashboard':\n                return {\n                    title: 'Dashboard',\n                    subtitle: 'Overview & analytics'\n                };\n            case '/playground':\n                return {\n                    title: 'Playground',\n                    subtitle: 'Test your models'\n                };\n            case '/my-models':\n                return {\n                    title: 'My Models',\n                    subtitle: 'API key management'\n                };\n            case '/routing-setup':\n                return {\n                    title: 'Routing Setup',\n                    subtitle: 'Configure routing'\n                };\n            case '/logs':\n                return {\n                    title: 'Logs',\n                    subtitle: 'Request history'\n                };\n            case '/training':\n                return {\n                    title: 'Prompt Engineering',\n                    subtitle: 'Custom prompts'\n                };\n            case '/analytics':\n                return {\n                    title: 'Analytics',\n                    subtitle: 'Advanced insights'\n                };\n            case '/add-keys':\n                return {\n                    title: 'Add Keys',\n                    subtitle: 'API key setup'\n                };\n            default:\n                return {\n                    title: 'Dashboard',\n                    subtitle: 'Overview'\n                };\n        }\n    };\n    const breadcrumb = getBreadcrumb(pathname);\n    // Display correct subscription tier name\n    const planName = (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'Free Plan' : (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'starter' ? 'Starter Plan' : (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'professional' ? 'Professional Plan' : (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'enterprise' ? 'Enterprise Plan' : 'Free Plan'; // Default to Free Plan\n    const handleSignOut = async ()=>{\n        try {\n            // Clear all caches and storage before signing out\n            localStorage.clear();\n            sessionStorage.clear();\n            // Clear advanced cache\n            try {\n                const { globalCache } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/utils/advancedCache */ \"(app-pages-browser)/./src/utils/advancedCache.ts\"));\n                globalCache.clear();\n                console.log('Advanced cache cleared');\n            } catch (cacheError) {\n                console.warn('Could not clear advanced cache:', cacheError);\n            }\n            await supabase.auth.signOut();\n            // Force a hard reload to clear any remaining cached data\n            window.location.href = '/auth/signin';\n        } catch (err) {\n            console.error('Sign out error:', err);\n            // Even if sign out fails, clear caches and redirect\n            localStorage.clear();\n            sessionStorage.clear();\n            window.location.href = '/auth/signin';\n        }\n    };\n    // Handle search keyboard shortcut\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"Navbar.useEffect.handleKeyDown\": (e)=>{\n                    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {\n                        e.preventDefault();\n                        setIsSearchOpen(true);\n                    }\n                }\n            }[\"Navbar.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"Navbar.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"header border-b border-gray-200 bg-white/95 backdrop-blur-sm w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 sm:px-6 lg:px-8 \".concat(// When sidebar is expanded, use standard max width with centering\n                // When sidebar is collapsed, use full width with padding\n                isDesktop && (!isCollapsed || isHovered) ? 'max-w-7xl mx-auto' : isDesktop ? 'max-w-none' : 'max-w-7xl mx-auto'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleSidebar,\n                                    className: \"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\",\n                                    title: \"Toggle sidebar\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"RouKey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:flex items-center space-x-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: breadcrumb.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"/\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-900 font-medium\",\n                                            children: breadcrumb.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden xl:block relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsSearchOpen(true),\n                                            className: \"w-64 pl-10 pr-4 py-2.5 text-sm bg-white border border-gray-200 rounded-lg text-gray-500 hover:text-gray-700 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200 text-left flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Search...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                    className: \"hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium text-gray-400 bg-gray-100 border border-gray-200 rounded\",\n                                                    children: \"⌘K\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsSearchOpen(true),\n                                    className: \"xl:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:block relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsSettingsOpen(!isSettingsOpen),\n                                            className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-3 w-3 text-gray-600 transition-transform duration-200 \".concat(isSettingsOpen ? 'rotate-180' : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        isSettingsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"fixed inset-0 z-10\",\n                                                    onClick: ()=>setIsSettingsOpen(false)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: \"/settings\",\n                                                            className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200\",\n                                                            onClick: ()=>setIsSettingsOpen(false),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-3 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Account Settings\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: \"/billing\",\n                                                            className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200\",\n                                                            onClick: ()=>setIsSettingsOpen(false),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-3 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                    lineNumber: 215,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Billing & Plans\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                            className: \"my-1 border-gray-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleSignOut,\n                                                            className: \"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-3 text-red-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Sign Out\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-semibold text-sm\",\n                                                children: initials\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: firstName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: planName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlobalSearch__WEBPACK_IMPORTED_MODULE_7__.GlobalSearch, {\n                isOpen: isSearchOpen,\n                onClose: ()=>setIsSearchOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, this);\n}\n_s(Navbar, \"OkHQNbYz4BOJIatka67wO9DkOXc=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_3__.useSidebar,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__.useSubscription\n    ];\n});\n_c = Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL05hdmJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNkI7QUFDZTtBQUNzSTtBQUMzSDtBQUNUO0FBQ1k7QUFDVTtBQUN0QjtBQUUvQixTQUFTZTtRQXdCSkMscUJBQW1DQSwrQkFBQUEsc0JBQ0NBLHNDQUFBQSwrQkFBQUEsc0JBQTREQzs7SUF4QmxILE1BQU0sRUFBRUMsV0FBVyxFQUFFQyxTQUFTLEVBQUVDLGFBQWEsRUFBRSxHQUFHVixvRUFBVUE7SUFDNUQsTUFBTVcsV0FBV1YsNERBQVdBO0lBQzVCLE1BQU0sRUFBRUssSUFBSSxFQUFFTSxrQkFBa0IsRUFBRSxHQUFHVix1RUFBZUE7SUFDcEQsTUFBTSxDQUFDVyxnQkFBZ0JDLGtCQUFrQixHQUFHdkIsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDd0IsY0FBY0MsZ0JBQWdCLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUMwQixXQUFXQyxhQUFhLEdBQUczQiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNNEIsV0FBV2hCLGlGQUEyQkE7SUFFNUMsc0RBQXNEO0lBQ3REWCxnREFBU0E7NEJBQUM7WUFDUixNQUFNNEI7bURBQWlCO29CQUNyQkYsYUFBYUcsT0FBT0MsVUFBVSxJQUFJLE9BQU8sZ0JBQWdCO2dCQUMzRDs7WUFFQSxpQkFBaUI7WUFDakJGO1lBRUEsMkJBQTJCO1lBQzNCQyxPQUFPRSxnQkFBZ0IsQ0FBQyxVQUFVSDtZQUNsQztvQ0FBTyxJQUFNQyxPQUFPRyxtQkFBbUIsQ0FBQyxVQUFVSjs7UUFDcEQ7MkJBQUcsRUFBRTtJQUVMLHdCQUF3QjtJQUN4QixNQUFNYixZQUFZRCxDQUFBQSxpQkFBQUEsNEJBQUFBLHNCQUFBQSxLQUFNbUIsYUFBYSxjQUFuQm5CLDBDQUFBQSxvQkFBcUJvQixVQUFVLE1BQUlwQixpQkFBQUEsNEJBQUFBLHVCQUFBQSxLQUFNbUIsYUFBYSxjQUFuQm5CLDRDQUFBQSxnQ0FBQUEscUJBQXFCcUIsU0FBUyxjQUE5QnJCLG9EQUFBQSw4QkFBZ0NzQixLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsS0FBSTtJQUN0RyxNQUFNQyxXQUFXdEIsVUFBVXVCLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQU16QixDQUFBQSxDQUFBQSxpQkFBQUEsNEJBQUFBLHVCQUFBQSxLQUFNbUIsYUFBYSxjQUFuQm5CLDRDQUFBQSxnQ0FBQUEscUJBQXFCMEIsU0FBUyxjQUE5QjFCLHFEQUFBQSx1Q0FBQUEsOEJBQWdDd0IsTUFBTSxDQUFDLGdCQUF2Q3hCLDJEQUFBQSxxQ0FBMkN5QixXQUFXLFNBQU14QixvQkFBQUEsVUFBVXVCLE1BQU0sQ0FBQyxnQkFBakJ2Qix3Q0FBQUEsa0JBQXFCd0IsV0FBVyxPQUFNLEdBQUU7SUFFMUosMkNBQTJDO0lBQzNDLE1BQU1FLGdCQUFnQixDQUFDQztRQUNyQixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gsT0FBTztvQkFBRUMsT0FBTztvQkFBYUMsVUFBVTtnQkFBdUI7WUFDaEUsS0FBSztnQkFDSCxPQUFPO29CQUFFRCxPQUFPO29CQUFjQyxVQUFVO2dCQUFtQjtZQUM3RCxLQUFLO2dCQUNILE9BQU87b0JBQUVELE9BQU87b0JBQWFDLFVBQVU7Z0JBQXFCO1lBQzlELEtBQUs7Z0JBQ0gsT0FBTztvQkFBRUQsT0FBTztvQkFBaUJDLFVBQVU7Z0JBQW9CO1lBQ2pFLEtBQUs7Z0JBQ0gsT0FBTztvQkFBRUQsT0FBTztvQkFBUUMsVUFBVTtnQkFBa0I7WUFDdEQsS0FBSztnQkFDSCxPQUFPO29CQUFFRCxPQUFPO29CQUFzQkMsVUFBVTtnQkFBaUI7WUFDbkUsS0FBSztnQkFDSCxPQUFPO29CQUFFRCxPQUFPO29CQUFhQyxVQUFVO2dCQUFvQjtZQUM3RCxLQUFLO2dCQUNILE9BQU87b0JBQUVELE9BQU87b0JBQVlDLFVBQVU7Z0JBQWdCO1lBQ3hEO2dCQUNFLE9BQU87b0JBQUVELE9BQU87b0JBQWFDLFVBQVU7Z0JBQVc7UUFDdEQ7SUFDRjtJQUVBLE1BQU1DLGFBQWFKLGNBQWN0QjtJQUVqQyx5Q0FBeUM7SUFDekMsTUFBTTJCLFdBQVcxQixDQUFBQSwrQkFBQUEseUNBQUFBLG1CQUFvQjJCLElBQUksTUFBSyxTQUFTLGNBQ3RDM0IsQ0FBQUEsK0JBQUFBLHlDQUFBQSxtQkFBb0IyQixJQUFJLE1BQUssWUFBWSxpQkFDekMzQixDQUFBQSwrQkFBQUEseUNBQUFBLG1CQUFvQjJCLElBQUksTUFBSyxpQkFBaUIsc0JBQzlDM0IsQ0FBQUEsK0JBQUFBLHlDQUFBQSxtQkFBb0IyQixJQUFJLE1BQUssZUFBZSxvQkFDNUMsYUFBYSx1QkFBdUI7SUFFckQsTUFBTUMsZ0JBQWdCO1FBQ3BCLElBQUk7WUFDRixrREFBa0Q7WUFDbERDLGFBQWFDLEtBQUs7WUFDbEJDLGVBQWVELEtBQUs7WUFFcEIsdUJBQXVCO1lBQ3ZCLElBQUk7Z0JBQ0YsTUFBTSxFQUFFRSxXQUFXLEVBQUUsR0FBRyxNQUFNLHFLQUErQjtnQkFDN0RBLFlBQVlGLEtBQUs7Z0JBQ2pCRyxRQUFRQyxHQUFHLENBQUM7WUFDZCxFQUFFLE9BQU9DLFlBQVk7Z0JBQ25CRixRQUFRRyxJQUFJLENBQUMsbUNBQW1DRDtZQUNsRDtZQUVBLE1BQU01QixTQUFTOEIsSUFBSSxDQUFDQyxPQUFPO1lBRTNCLHlEQUF5RDtZQUN6RDdCLE9BQU84QixRQUFRLENBQUNDLElBQUksR0FBRztRQUN6QixFQUFFLE9BQU9DLEtBQUs7WUFDWlIsUUFBUVMsS0FBSyxDQUFDLG1CQUFtQkQ7WUFDakMsb0RBQW9EO1lBQ3BEWixhQUFhQyxLQUFLO1lBQ2xCQyxlQUFlRCxLQUFLO1lBQ3BCckIsT0FBTzhCLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO1FBQ3pCO0lBQ0Y7SUFFQSxrQ0FBa0M7SUFDbEM1RCxnREFBU0E7NEJBQUM7WUFDUixNQUFNK0Q7a0RBQWdCLENBQUNDO29CQUNyQixJQUFJLENBQUNBLEVBQUVDLE9BQU8sSUFBSUQsRUFBRUUsT0FBTyxLQUFLRixFQUFFRyxHQUFHLEtBQUssS0FBSzt3QkFDN0NILEVBQUVJLGNBQWM7d0JBQ2hCNUMsZ0JBQWdCO29CQUNsQjtnQkFDRjs7WUFFQTZDLFNBQVN0QyxnQkFBZ0IsQ0FBQyxXQUFXZ0M7WUFDckM7b0NBQU8sSUFBTU0sU0FBU3JDLG1CQUFtQixDQUFDLFdBQVcrQjs7UUFDdkQ7MkJBQUcsRUFBRTtJQUNMLHFCQUNFLDhEQUFDTztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQUlELFdBQVcsd0JBUWYsT0FQQyxrRUFBa0U7Z0JBQ2xFLHlEQUF5RDtnQkFDekQ5QyxhQUFjLEVBQUNULGVBQWVDLFNBQVEsSUFDbEMsc0JBQ0FRLFlBQ0UsZUFDQTswQkFFTiw0RUFBQytDO29CQUFJRCxXQUFVOztzQ0FFYiw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUViLDhEQUFDRTtvQ0FDQ0MsU0FBU3hEO29DQUNUcUQsV0FBVTtvQ0FDVjVCLE9BQU07OENBRU4sNEVBQUN4Qyx1TUFBU0E7d0NBQUNvRSxXQUFVOzs7Ozs7Ozs7Ozs4Q0FJdkIsOERBQUNDO29DQUFJRCxXQUFVOzhDQUNiLDRFQUFDSTt3Q0FBR0osV0FBVTtrREFBa0M7Ozs7Ozs7Ozs7OzhDQUlsRCw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDSztzREFBTS9CLFdBQVdGLEtBQUs7Ozs7OztzREFDdkIsOERBQUNpQztzREFBSzs7Ozs7O3NEQUNOLDhEQUFDQTs0Q0FBS0wsV0FBVTtzREFBNkIxQixXQUFXRCxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS3BFLDhEQUFDNEI7NEJBQUlELFdBQVU7OzhDQUViLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNFOzRDQUNDQyxTQUFTLElBQU1sRCxnQkFBZ0I7NENBQy9CK0MsV0FBVTs7OERBRVYsOERBQUNLOzhEQUFLOzs7Ozs7OERBQ04sOERBQUNDO29EQUFJTixXQUFVOzhEQUE0SDs7Ozs7Ozs7Ozs7O3NEQUk3SSw4REFBQ0M7NENBQUlELFdBQVU7c0RBQ2IsNEVBQUNqRSx1TUFBbUJBO2dEQUFDaUUsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS25DLDhEQUFDRTtvQ0FDQ0MsU0FBUyxJQUFNbEQsZ0JBQWdCO29DQUMvQitDLFdBQVU7OENBRVYsNEVBQUNqRSx1TUFBbUJBO3dDQUFDaUUsV0FBVTs7Ozs7Ozs7Ozs7OENBSWpDLDhEQUFDRTtvQ0FBT0YsV0FBVTs7c0RBQ2hCLDhEQUFDdEUsd01BQVFBOzRDQUFDc0UsV0FBVTs7Ozs7O3NEQUNwQiw4REFBQ0s7NENBQUtMLFdBQVU7Ozs7Ozs7Ozs7Ozs4Q0FJbEIsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0U7NENBQ0NDLFNBQVMsSUFBTXBELGtCQUFrQixDQUFDRDs0Q0FDbENrRCxXQUFVOzs4REFFViw4REFBQ3JFLHdNQUFhQTtvREFBQ3FFLFdBQVU7Ozs7Ozs4REFDekIsOERBQUNsRSx3TUFBZUE7b0RBQUNrRSxXQUFXLDJEQUE4RixPQUFuQ2xELGlCQUFpQixlQUFlOzs7Ozs7Ozs7Ozs7d0NBSXhIQSxnQ0FDQzs7OERBRUUsOERBQUNtRDtvREFDQ0QsV0FBVTtvREFDVkcsU0FBUyxJQUFNcEQsa0JBQWtCOzs7Ozs7OERBSW5DLDhEQUFDa0Q7b0RBQUlELFdBQVU7O3NFQUNiLDhEQUFDekUsa0RBQUlBOzREQUNIOEQsTUFBSzs0REFDTFcsV0FBVTs0REFDVkcsU0FBUyxJQUFNcEQsa0JBQWtCOzs4RUFFakMsOERBQUNwQix3TUFBYUE7b0VBQUNxRSxXQUFVOzs7Ozs7Z0VBQStCOzs7Ozs7O3NFQUkxRCw4REFBQ3pFLGtEQUFJQTs0REFDSDhELE1BQUs7NERBQ0xXLFdBQVU7NERBQ1ZHLFNBQVMsSUFBTXBELGtCQUFrQjs7OEVBRWpDLDhEQUFDZix3TUFBY0E7b0VBQUNnRSxXQUFVOzs7Ozs7Z0VBQStCOzs7Ozs7O3NFQUkzRCw4REFBQ087NERBQUdQLFdBQVU7Ozs7OztzRUFFZCw4REFBQ0U7NERBQ0NDLFNBQVMxQjs0REFDVHVCLFdBQVU7OzhFQUVWLDhEQUFDbkUsd01BQXlCQTtvRUFBQ21FLFdBQVU7Ozs7OztnRUFBOEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FTN0UsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7c0RBQ2IsNEVBQUNLO2dEQUFLTCxXQUFVOzBEQUFvQ2xDOzs7Ozs7Ozs7OztzREFFdEQsOERBQUNtQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNRO29EQUFFUixXQUFVOzhEQUFxQ3hEOzs7Ozs7OERBQ2xELDhEQUFDZ0U7b0RBQUVSLFdBQVU7OERBQXlCekI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFoRCw4REFBQ2xDLHVEQUFZQTtnQkFDWG9FLFFBQVF6RDtnQkFDUjBELFNBQVMsSUFBTXpELGdCQUFnQjs7Ozs7Ozs7Ozs7O0FBSXZDO0dBbFB3Qlg7O1FBQzRCTCxnRUFBVUE7UUFDM0NDLHdEQUFXQTtRQUNTQyxtRUFBZUE7OztLQUg5QkciLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxjb21wb25lbnRzXFxOYXZiYXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBVc2VyQ2lyY2xlSWNvbiwgQmVsbEljb24sIENvZzZUb290aEljb24sIEJhcnMzSWNvbiwgQXJyb3dSaWdodE9uUmVjdGFuZ2xlSWNvbiwgQ2hldnJvbkRvd25JY29uLCBNYWduaWZ5aW5nR2xhc3NJY29uLCBDcmVkaXRDYXJkSWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5pbXBvcnQgeyB1c2VTaWRlYmFyIH0gZnJvbSAnQC9jb250ZXh0cy9TaWRlYmFyQ29udGV4dCc7XG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyB1c2VTdWJzY3JpcHRpb24gfSBmcm9tICdAL2hvb2tzL3VzZVN1YnNjcmlwdGlvbic7XG5pbXBvcnQgeyBjcmVhdGVTdXBhYmFzZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAL2xpYi9zdXBhYmFzZS9jbGllbnQnO1xuaW1wb3J0IHsgR2xvYmFsU2VhcmNoIH0gZnJvbSAnLi9HbG9iYWxTZWFyY2gnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOYXZiYXIoKSB7XG4gIGNvbnN0IHsgaXNDb2xsYXBzZWQsIGlzSG92ZXJlZCwgdG9nZ2xlU2lkZWJhciB9ID0gdXNlU2lkZWJhcigpO1xuICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKCk7XG4gIGNvbnN0IHsgdXNlciwgc3Vic2NyaXB0aW9uU3RhdHVzIH0gPSB1c2VTdWJzY3JpcHRpb24oKTtcbiAgY29uc3QgW2lzU2V0dGluZ3NPcGVuLCBzZXRJc1NldHRpbmdzT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc1NlYXJjaE9wZW4sIHNldElzU2VhcmNoT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc0Rlc2t0b3AsIHNldElzRGVza3RvcF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlU3VwYWJhc2VCcm93c2VyQ2xpZW50KCk7XG5cbiAgLy8gVHJhY2sgaWYgd2UncmUgb24gZGVza3RvcCAobGcgYnJlYWtwb2ludCBhbmQgYWJvdmUpXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgY2hlY2tJc0Rlc2t0b3AgPSAoKSA9PiB7XG4gICAgICBzZXRJc0Rlc2t0b3Aod2luZG93LmlubmVyV2lkdGggPj0gMTAyNCk7IC8vIGxnIGJyZWFrcG9pbnRcbiAgICB9O1xuXG4gICAgLy8gQ2hlY2sgb24gbW91bnRcbiAgICBjaGVja0lzRGVza3RvcCgpO1xuXG4gICAgLy8gTGlzdGVuIGZvciByZXNpemUgZXZlbnRzXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGNoZWNrSXNEZXNrdG9wKTtcbiAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGNoZWNrSXNEZXNrdG9wKTtcbiAgfSwgW10pO1xuXG4gIC8vIEdldCB1c2VyIGRpc3BsYXkgaW5mb1xuICBjb25zdCBmaXJzdE5hbWUgPSB1c2VyPy51c2VyX21ldGFkYXRhPy5maXJzdF9uYW1lIHx8IHVzZXI/LnVzZXJfbWV0YWRhdGE/LmZ1bGxfbmFtZT8uc3BsaXQoJyAnKVswXSB8fCAnVXNlcic7XG4gIGNvbnN0IGluaXRpYWxzID0gZmlyc3ROYW1lLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgKHVzZXI/LnVzZXJfbWV0YWRhdGE/Lmxhc3RfbmFtZT8uY2hhckF0KDApPy50b1VwcGVyQ2FzZSgpIHx8IGZpcnN0TmFtZS5jaGFyQXQoMSk/LnRvVXBwZXJDYXNlKCkgfHwgJ1UnKTtcblxuICAvLyBHZXQgc2ltcGxlIGJyZWFkY3J1bWIgaW5mbyBmcm9tIHBhdGhuYW1lXG4gIGNvbnN0IGdldEJyZWFkY3J1bWIgPSAocGF0aDogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChwYXRoKSB7XG4gICAgICBjYXNlICcvZGFzaGJvYXJkJzpcbiAgICAgICAgcmV0dXJuIHsgdGl0bGU6ICdEYXNoYm9hcmQnLCBzdWJ0aXRsZTogJ092ZXJ2aWV3ICYgYW5hbHl0aWNzJyB9O1xuICAgICAgY2FzZSAnL3BsYXlncm91bmQnOlxuICAgICAgICByZXR1cm4geyB0aXRsZTogJ1BsYXlncm91bmQnLCBzdWJ0aXRsZTogJ1Rlc3QgeW91ciBtb2RlbHMnIH07XG4gICAgICBjYXNlICcvbXktbW9kZWxzJzpcbiAgICAgICAgcmV0dXJuIHsgdGl0bGU6ICdNeSBNb2RlbHMnLCBzdWJ0aXRsZTogJ0FQSSBrZXkgbWFuYWdlbWVudCcgfTtcbiAgICAgIGNhc2UgJy9yb3V0aW5nLXNldHVwJzpcbiAgICAgICAgcmV0dXJuIHsgdGl0bGU6ICdSb3V0aW5nIFNldHVwJywgc3VidGl0bGU6ICdDb25maWd1cmUgcm91dGluZycgfTtcbiAgICAgIGNhc2UgJy9sb2dzJzpcbiAgICAgICAgcmV0dXJuIHsgdGl0bGU6ICdMb2dzJywgc3VidGl0bGU6ICdSZXF1ZXN0IGhpc3RvcnknIH07XG4gICAgICBjYXNlICcvdHJhaW5pbmcnOlxuICAgICAgICByZXR1cm4geyB0aXRsZTogJ1Byb21wdCBFbmdpbmVlcmluZycsIHN1YnRpdGxlOiAnQ3VzdG9tIHByb21wdHMnIH07XG4gICAgICBjYXNlICcvYW5hbHl0aWNzJzpcbiAgICAgICAgcmV0dXJuIHsgdGl0bGU6ICdBbmFseXRpY3MnLCBzdWJ0aXRsZTogJ0FkdmFuY2VkIGluc2lnaHRzJyB9O1xuICAgICAgY2FzZSAnL2FkZC1rZXlzJzpcbiAgICAgICAgcmV0dXJuIHsgdGl0bGU6ICdBZGQgS2V5cycsIHN1YnRpdGxlOiAnQVBJIGtleSBzZXR1cCcgfTtcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiB7IHRpdGxlOiAnRGFzaGJvYXJkJywgc3VidGl0bGU6ICdPdmVydmlldycgfTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgYnJlYWRjcnVtYiA9IGdldEJyZWFkY3J1bWIocGF0aG5hbWUpO1xuXG4gIC8vIERpc3BsYXkgY29ycmVjdCBzdWJzY3JpcHRpb24gdGllciBuYW1lXG4gIGNvbnN0IHBsYW5OYW1lID0gc3Vic2NyaXB0aW9uU3RhdHVzPy50aWVyID09PSAnZnJlZScgPyAnRnJlZSBQbGFuJyA6XG4gICAgICAgICAgICAgICAgICAgc3Vic2NyaXB0aW9uU3RhdHVzPy50aWVyID09PSAnc3RhcnRlcicgPyAnU3RhcnRlciBQbGFuJyA6XG4gICAgICAgICAgICAgICAgICAgc3Vic2NyaXB0aW9uU3RhdHVzPy50aWVyID09PSAncHJvZmVzc2lvbmFsJyA/ICdQcm9mZXNzaW9uYWwgUGxhbicgOlxuICAgICAgICAgICAgICAgICAgIHN1YnNjcmlwdGlvblN0YXR1cz8udGllciA9PT0gJ2VudGVycHJpc2UnID8gJ0VudGVycHJpc2UgUGxhbicgOlxuICAgICAgICAgICAgICAgICAgICdGcmVlIFBsYW4nOyAvLyBEZWZhdWx0IHRvIEZyZWUgUGxhblxuXG4gIGNvbnN0IGhhbmRsZVNpZ25PdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIENsZWFyIGFsbCBjYWNoZXMgYW5kIHN0b3JhZ2UgYmVmb3JlIHNpZ25pbmcgb3V0XG4gICAgICBsb2NhbFN0b3JhZ2UuY2xlYXIoKTtcbiAgICAgIHNlc3Npb25TdG9yYWdlLmNsZWFyKCk7XG5cbiAgICAgIC8vIENsZWFyIGFkdmFuY2VkIGNhY2hlXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCB7IGdsb2JhbENhY2hlIH0gPSBhd2FpdCBpbXBvcnQoJ0AvdXRpbHMvYWR2YW5jZWRDYWNoZScpO1xuICAgICAgICBnbG9iYWxDYWNoZS5jbGVhcigpO1xuICAgICAgICBjb25zb2xlLmxvZygnQWR2YW5jZWQgY2FjaGUgY2xlYXJlZCcpO1xuICAgICAgfSBjYXRjaCAoY2FjaGVFcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oJ0NvdWxkIG5vdCBjbGVhciBhZHZhbmNlZCBjYWNoZTonLCBjYWNoZUVycm9yKTtcbiAgICAgIH1cblxuICAgICAgYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduT3V0KCk7XG5cbiAgICAgIC8vIEZvcmNlIGEgaGFyZCByZWxvYWQgdG8gY2xlYXIgYW55IHJlbWFpbmluZyBjYWNoZWQgZGF0YVxuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2F1dGgvc2lnbmluJztcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1NpZ24gb3V0IGVycm9yOicsIGVycik7XG4gICAgICAvLyBFdmVuIGlmIHNpZ24gb3V0IGZhaWxzLCBjbGVhciBjYWNoZXMgYW5kIHJlZGlyZWN0XG4gICAgICBsb2NhbFN0b3JhZ2UuY2xlYXIoKTtcbiAgICAgIHNlc3Npb25TdG9yYWdlLmNsZWFyKCk7XG4gICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvYXV0aC9zaWduaW4nO1xuICAgIH1cbiAgfTtcblxuICAvLyBIYW5kbGUgc2VhcmNoIGtleWJvYXJkIHNob3J0Y3V0XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlS2V5RG93biA9IChlOiBLZXlib2FyZEV2ZW50KSA9PiB7XG4gICAgICBpZiAoKGUubWV0YUtleSB8fCBlLmN0cmxLZXkpICYmIGUua2V5ID09PSAnaycpIHtcbiAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICBzZXRJc1NlYXJjaE9wZW4odHJ1ZSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVLZXlEb3duKTtcbiAgICByZXR1cm4gKCkgPT4gZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleURvd24pO1xuICB9LCBbXSk7XG4gIHJldHVybiAoXG4gICAgPG5hdiBjbGFzc05hbWU9XCJoZWFkZXIgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIGJnLXdoaXRlLzk1IGJhY2tkcm9wLWJsdXItc20gdy1mdWxsXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YHB4LTQgc206cHgtNiBsZzpweC04ICR7XG4gICAgICAgIC8vIFdoZW4gc2lkZWJhciBpcyBleHBhbmRlZCwgdXNlIHN0YW5kYXJkIG1heCB3aWR0aCB3aXRoIGNlbnRlcmluZ1xuICAgICAgICAvLyBXaGVuIHNpZGViYXIgaXMgY29sbGFwc2VkLCB1c2UgZnVsbCB3aWR0aCB3aXRoIHBhZGRpbmdcbiAgICAgICAgaXNEZXNrdG9wICYmICghaXNDb2xsYXBzZWQgfHwgaXNIb3ZlcmVkKVxuICAgICAgICAgID8gJ21heC13LTd4bCBteC1hdXRvJ1xuICAgICAgICAgIDogaXNEZXNrdG9wXG4gICAgICAgICAgICA/ICdtYXgtdy1ub25lJ1xuICAgICAgICAgICAgOiAnbWF4LXctN3hsIG14LWF1dG8nXG4gICAgICB9YH0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIGgtMTZcIj5cbiAgICAgICAgICB7LyogTGVmdCBzaWRlIC0gTW9iaWxlIG1lbnUgYnV0dG9uIGFuZCBicmVhZGNydW1iICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICB7LyogTW9iaWxlIG1lbnUgYnV0dG9uIC0gdmlzaWJsZSBvbiBtb2JpbGUgb25seSAqL31cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlU2lkZWJhcn1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibGc6aGlkZGVuIHAtMiByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktMTAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgIHRpdGxlPVwiVG9nZ2xlIHNpZGViYXJcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8QmFyczNJY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1ncmF5LTYwMFwiIC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgey8qIE1vYmlsZSBsb2dvIC0gdmlzaWJsZSBvbiBtb2JpbGUgb25seSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6aGlkZGVuXCI+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+Um91S2V5PC9oMT5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogRHluYW1pYyBCcmVhZGNydW1iIC0gaGlkZGVuIG9uIG1vYmlsZSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiB0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgPHNwYW4+e2JyZWFkY3J1bWIudGl0bGV9PC9zcGFuPlxuICAgICAgICAgICAgICA8c3Bhbj4vPC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwIGZvbnQtbWVkaXVtXCI+e2JyZWFkY3J1bWIuc3VidGl0bGV9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUmlnaHQgc2lkZSAtIHJlc3BvbnNpdmUgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgc206c3BhY2UteC00XCI+XG4gICAgICAgICAgICB7LyogU2VhcmNoIC0gSGlkZGVuIG9uIG1vYmlsZSwgdmlzaWJsZSBvbiBsYXJnZXIgc2NyZWVucyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIHhsOmJsb2NrIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc1NlYXJjaE9wZW4odHJ1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy02NCBwbC0xMCBwci00IHB5LTIuNSB0ZXh0LXNtIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1sZyB0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vcmFuZ2UtNTAwLzIwIGZvY3VzOmJvcmRlci1vcmFuZ2UtNTAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCB0ZXh0LWxlZnQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxzcGFuPlNlYXJjaC4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8a2JkIGNsYXNzTmFtZT1cImhpZGRlbiBzbTppbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0xIHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTQwMCBiZy1ncmF5LTEwMCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgIOKMmEtcbiAgICAgICAgICAgICAgICA8L2tiZD5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQteS0wIGxlZnQtMCBwbC0zIGZsZXggaXRlbXMtY2VudGVyIHBvaW50ZXItZXZlbnRzLW5vbmVcIj5cbiAgICAgICAgICAgICAgICA8TWFnbmlmeWluZ0dsYXNzSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTW9iaWxlIFNlYXJjaCBCdXR0b24gKi99XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzU2VhcmNoT3Blbih0cnVlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwieGw6aGlkZGVuIHAtMiByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktMTAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxNYWduaWZ5aW5nR2xhc3NJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmF5LTYwMFwiIC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgey8qIE5vdGlmaWNhdGlvbnMgLSBBbHdheXMgdmlzaWJsZSAqL31cbiAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicC0yIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgIDxCZWxsSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JheS02MDBcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTEgLXJpZ2h0LTEgaC0zIHctMyBiZy1vcmFuZ2UtNTAwIHJvdW5kZWQtZnVsbFwiPjwvc3Bhbj5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICB7LyogU2V0dGluZ3MgRHJvcGRvd24gLSBIaWRkZW4gb24gbW9iaWxlICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gc206YmxvY2sgcmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzU2V0dGluZ3NPcGVuKCFpc1NldHRpbmdzT3Blbil9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8Q29nNlRvb3RoSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JheS02MDBcIiAvPlxuICAgICAgICAgICAgICAgIDxDaGV2cm9uRG93bkljb24gY2xhc3NOYW1lPXtgaC0zIHctMyB0ZXh0LWdyYXktNjAwIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTIwMCAke2lzU2V0dGluZ3NPcGVuID8gJ3JvdGF0ZS0xODAnIDogJyd9YH0gLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgey8qIFNldHRpbmdzIERyb3Bkb3duIE1lbnUgKi99XG4gICAgICAgICAgICAgIHtpc1NldHRpbmdzT3BlbiAmJiAoXG4gICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgIHsvKiBCYWNrZHJvcCAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCB6LTEwXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNTZXR0aW5nc09wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgICAgey8qIERyb3Bkb3duICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0wIG10LTIgdy00OCBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHB5LTEgei0yMFwiPlxuICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvc2V0dGluZ3NcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc1NldHRpbmdzT3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8Q29nNlRvb3RoSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTMgdGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgQWNjb3VudCBTZXR0aW5nc1xuICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG5cbiAgICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL2JpbGxpbmdcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc1NldHRpbmdzT3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8Q3JlZGl0Q2FyZEljb24gY2xhc3NOYW1lPVwiaC00IHctNCBtci0zIHRleHQtZ3JheS01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIEJpbGxpbmcgJiBQbGFuc1xuICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG5cbiAgICAgICAgICAgICAgICAgICAgPGhyIGNsYXNzTmFtZT1cIm15LTEgYm9yZGVyLWdyYXktMjAwXCIgLz5cblxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2lnbk91dH1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB3LWZ1bGwgcHgtNCBweS0yIHRleHQtc20gdGV4dC1yZWQtNjAwIGhvdmVyOmJnLXJlZC01MCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPEFycm93UmlnaHRPblJlY3RhbmdsZUljb24gY2xhc3NOYW1lPVwiaC00IHctNCBtci0zIHRleHQtcmVkLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgU2lnbiBPdXRcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogVXNlciBQcm9maWxlIC0gUmVzcG9uc2l2ZSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHB4LTIgc206cHgtMyBweS0yIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGN1cnNvci1wb2ludGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCByb3VuZGVkLWZ1bGwgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1vcmFuZ2UtNDAwIHRvLW9yYW5nZS01MDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgdGV4dC1zbVwiPntpbml0aWFsc308L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBtZDpibG9ja1wiPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPntmaXJzdE5hbWV9PC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPntwbGFuTmFtZX08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBHbG9iYWwgU2VhcmNoIE1vZGFsICovfVxuICAgICAgPEdsb2JhbFNlYXJjaFxuICAgICAgICBpc09wZW49e2lzU2VhcmNoT3Blbn1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNTZWFyY2hPcGVuKGZhbHNlKX1cbiAgICAgIC8+XG4gICAgPC9uYXY+XG4gICk7XG59Il0sIm5hbWVzIjpbIkxpbmsiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkJlbGxJY29uIiwiQ29nNlRvb3RoSWNvbiIsIkJhcnMzSWNvbiIsIkFycm93UmlnaHRPblJlY3RhbmdsZUljb24iLCJDaGV2cm9uRG93bkljb24iLCJNYWduaWZ5aW5nR2xhc3NJY29uIiwiQ3JlZGl0Q2FyZEljb24iLCJ1c2VTaWRlYmFyIiwidXNlUGF0aG5hbWUiLCJ1c2VTdWJzY3JpcHRpb24iLCJjcmVhdGVTdXBhYmFzZUJyb3dzZXJDbGllbnQiLCJHbG9iYWxTZWFyY2giLCJOYXZiYXIiLCJ1c2VyIiwiZmlyc3ROYW1lIiwiaXNDb2xsYXBzZWQiLCJpc0hvdmVyZWQiLCJ0b2dnbGVTaWRlYmFyIiwicGF0aG5hbWUiLCJzdWJzY3JpcHRpb25TdGF0dXMiLCJpc1NldHRpbmdzT3BlbiIsInNldElzU2V0dGluZ3NPcGVuIiwiaXNTZWFyY2hPcGVuIiwic2V0SXNTZWFyY2hPcGVuIiwiaXNEZXNrdG9wIiwic2V0SXNEZXNrdG9wIiwic3VwYWJhc2UiLCJjaGVja0lzRGVza3RvcCIsIndpbmRvdyIsImlubmVyV2lkdGgiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInVzZXJfbWV0YWRhdGEiLCJmaXJzdF9uYW1lIiwiZnVsbF9uYW1lIiwic3BsaXQiLCJpbml0aWFscyIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwibGFzdF9uYW1lIiwiZ2V0QnJlYWRjcnVtYiIsInBhdGgiLCJ0aXRsZSIsInN1YnRpdGxlIiwiYnJlYWRjcnVtYiIsInBsYW5OYW1lIiwidGllciIsImhhbmRsZVNpZ25PdXQiLCJsb2NhbFN0b3JhZ2UiLCJjbGVhciIsInNlc3Npb25TdG9yYWdlIiwiZ2xvYmFsQ2FjaGUiLCJjb25zb2xlIiwibG9nIiwiY2FjaGVFcnJvciIsIndhcm4iLCJhdXRoIiwic2lnbk91dCIsImxvY2F0aW9uIiwiaHJlZiIsImVyciIsImVycm9yIiwiaGFuZGxlS2V5RG93biIsImUiLCJtZXRhS2V5IiwiY3RybEtleSIsImtleSIsInByZXZlbnREZWZhdWx0IiwiZG9jdW1lbnQiLCJuYXYiLCJjbGFzc05hbWUiLCJkaXYiLCJidXR0b24iLCJvbkNsaWNrIiwiaDEiLCJzcGFuIiwia2JkIiwiaHIiLCJwIiwiaXNPcGVuIiwib25DbG9zZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Navbar.tsx\n"));

/***/ })

});