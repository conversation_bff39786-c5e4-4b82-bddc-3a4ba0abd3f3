"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useSubscription.ts":
/*!**************************************!*\
  !*** ./src/hooks/useSubscription.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSubscription: () => (/* binding */ useSubscription)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n\n\nfunction useSubscription() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [subscriptionStatus, setSubscriptionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [usageStatus, setUsageStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__.createSupabaseBrowserClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSubscription.useEffect\": ()=>{\n            // Get initial user (more secure than getSession)\n            const getUser = {\n                \"useSubscription.useEffect.getUser\": async ()=>{\n                    const { data: { user } } = await supabase.auth.getUser();\n                    setUser(user !== null && user !== void 0 ? user : null);\n                    if (user) {\n                        fetchSubscriptionStatus(user);\n                        fetchUsageStatus(user);\n                    } else {\n                        setLoading(false);\n                    }\n                }\n            }[\"useSubscription.useEffect.getUser\"];\n            getUser();\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"useSubscription.useEffect\": async (event, session)=>{\n                    console.log('Auth state change in useSubscription:', event, !!session);\n                    var _session_user;\n                    setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        // Clear any cached data when switching users\n                        setSubscriptionStatus(null);\n                        setUsageStatus(null);\n                        setLoading(true);\n                        // Clear user-specific cache entries\n                        try {\n                            const { globalCache } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_utils_advancedCache_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/utils/advancedCache */ \"(app-pages-browser)/./src/utils/advancedCache.ts\"));\n                            globalCache.invalidateByTags([\n                                'user',\n                                'subscription',\n                                'usage'\n                            ]);\n                            console.log('User-specific cache invalidated for new user');\n                        } catch (cacheError) {\n                            console.warn('Could not invalidate user cache:', cacheError);\n                        }\n                        // Fetch fresh data for the new user\n                        await Promise.all([\n                            fetchSubscriptionStatus(session.user),\n                            fetchUsageStatus(session.user)\n                        ]);\n                    } else {\n                        // User signed out - clear all data\n                        setSubscriptionStatus(null);\n                        setUsageStatus(null);\n                        setError(null);\n                        setLoading(false);\n                    }\n                }\n            }[\"useSubscription.useEffect\"]);\n            return ({\n                \"useSubscription.useEffect\": ()=>subscription.unsubscribe()\n            })[\"useSubscription.useEffect\"];\n        }\n    }[\"useSubscription.useEffect\"], []);\n    const fetchSubscriptionStatus = async (currentUser)=>{\n        try {\n            console.log('Fetching subscription status for user:', currentUser.id);\n            const response = await fetch(\"/api/stripe/subscription-status?userId=\".concat(currentUser.id));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Subscription status API error:', {\n                    status: response.status,\n                    statusText: response.statusText,\n                    errorText\n                });\n                throw new Error(\"Failed to fetch subscription status: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log('Subscription status data:', data);\n            setSubscriptionStatus(data);\n        } catch (err) {\n            console.error('Error fetching subscription status:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const fetchUsageStatus = async (currentUser)=>{\n        try {\n            const response = await fetch('/api/stripe/subscription-status', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userId: currentUser.id\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch usage status');\n            }\n            const data = await response.json();\n            setUsageStatus(data);\n        } catch (err) {\n            console.error('Error fetching usage status:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createCheckoutSession = async (tier)=>{\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const response = await fetch('/api/stripe/create-checkout-session', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                priceId: getPriceIdForTier(tier),\n                userId: user.id,\n                userEmail: user.email,\n                tier\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || 'Failed to create checkout session');\n        }\n        const { url } = await response.json();\n        window.location.href = url;\n    };\n    const openCustomerPortal = async ()=>{\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const response = await fetch('/api/stripe/customer-portal', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                userId: user.id\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || 'Failed to open customer portal');\n        }\n        const { url } = await response.json();\n        window.location.href = url;\n    };\n    const refreshStatus = ()=>{\n        if (user) {\n            setLoading(true);\n            fetchSubscriptionStatus(user);\n            fetchUsageStatus(user);\n        }\n    };\n    return {\n        subscriptionStatus,\n        usageStatus,\n        loading,\n        error,\n        createCheckoutSession,\n        openCustomerPortal,\n        refreshStatus,\n        isAuthenticated: !!user,\n        user\n    };\n}\nfunction getPriceIdForTier(tier) {\n    switch(tier){\n        case 'free':\n            return \"price_1RcfRzC97XFBBUvd9XqkXe3a\";\n        case 'starter':\n            return \"price_1RcfWSC97XFBBUvdYHXbBvQ6\";\n        case 'professional':\n            return \"price_1RcfViC97XFBBUvdxwixKUdg\";\n        case 'enterprise':\n            return \"price_1RaADDC97XFBBUvd7j6OPJj7\";\n        default:\n            throw new Error(\"Invalid tier: \".concat(tier));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSubscription.ts\n"));

/***/ })

});