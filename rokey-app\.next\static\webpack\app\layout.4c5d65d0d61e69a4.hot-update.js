"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"048135918dd3\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjA0ODEzNTkxOGRkM1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,CreditCardIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,CreditCardIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,CreditCardIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,CreditCardIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,CreditCardIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,CreditCardIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,CreditCardIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _GlobalSearch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./GlobalSearch */ \"(app-pages-browser)/./src/components/GlobalSearch.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Navbar() {\n    var _user_user_metadata, _user_user_metadata_full_name, _user_user_metadata1, _user_user_metadata_last_name_charAt, _user_user_metadata_last_name, _user_user_metadata2, _firstName_charAt;\n    _s();\n    const { isCollapsed, isHovered, toggleSidebar } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_3__.useSidebar)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const { user, subscriptionStatus, refreshStatus } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__.useSubscription)();\n    const [isSettingsOpen, setIsSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isSearchOpen, setIsSearchOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isDesktop, setIsDesktop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_6__.createSupabaseBrowserClient)();\n    // Track if we're on desktop (lg breakpoint and above)\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const checkIsDesktop = {\n                \"Navbar.useEffect.checkIsDesktop\": ()=>{\n                    setIsDesktop(window.innerWidth >= 1024); // lg breakpoint\n                }\n            }[\"Navbar.useEffect.checkIsDesktop\"];\n            // Check on mount\n            checkIsDesktop();\n            // Listen for resize events\n            window.addEventListener('resize', checkIsDesktop);\n            return ({\n                \"Navbar.useEffect\": ()=>window.removeEventListener('resize', checkIsDesktop)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    // Get user display info\n    const firstName = (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.first_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : (_user_user_metadata_full_name = _user_user_metadata1.full_name) === null || _user_user_metadata_full_name === void 0 ? void 0 : _user_user_metadata_full_name.split(' ')[0]) || 'User';\n    const initials = firstName.charAt(0).toUpperCase() + ((user === null || user === void 0 ? void 0 : (_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : (_user_user_metadata_last_name = _user_user_metadata2.last_name) === null || _user_user_metadata_last_name === void 0 ? void 0 : (_user_user_metadata_last_name_charAt = _user_user_metadata_last_name.charAt(0)) === null || _user_user_metadata_last_name_charAt === void 0 ? void 0 : _user_user_metadata_last_name_charAt.toUpperCase()) || ((_firstName_charAt = firstName.charAt(1)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || 'U');\n    // Get simple breadcrumb info from pathname\n    const getBreadcrumb = (path)=>{\n        switch(path){\n            case '/dashboard':\n                return {\n                    title: 'Dashboard',\n                    subtitle: 'Overview & analytics'\n                };\n            case '/playground':\n                return {\n                    title: 'Playground',\n                    subtitle: 'Test your models'\n                };\n            case '/my-models':\n                return {\n                    title: 'My Models',\n                    subtitle: 'API key management'\n                };\n            case '/routing-setup':\n                return {\n                    title: 'Routing Setup',\n                    subtitle: 'Configure routing'\n                };\n            case '/logs':\n                return {\n                    title: 'Logs',\n                    subtitle: 'Request history'\n                };\n            case '/training':\n                return {\n                    title: 'Prompt Engineering',\n                    subtitle: 'Custom prompts'\n                };\n            case '/analytics':\n                return {\n                    title: 'Analytics',\n                    subtitle: 'Advanced insights'\n                };\n            case '/add-keys':\n                return {\n                    title: 'Add Keys',\n                    subtitle: 'API key setup'\n                };\n            default:\n                return {\n                    title: 'Dashboard',\n                    subtitle: 'Overview'\n                };\n        }\n    };\n    const breadcrumb = getBreadcrumb(pathname);\n    // Display correct subscription tier name\n    const planName = (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'Free Plan' : (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'starter' ? 'Starter Plan' : (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'professional' ? 'Professional Plan' : (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'enterprise' ? 'Enterprise Plan' : 'Free Plan'; // Default to Free Plan\n    const handleSignOut = async ()=>{\n        try {\n            // Clear all caches and storage before signing out\n            const { clearAllUserCache } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_utils_clearUserCache_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/utils/clearUserCache */ \"(app-pages-browser)/./src/utils/clearUserCache.ts\"));\n            await clearAllUserCache();\n            await supabase.auth.signOut();\n            // Force a hard reload to clear any remaining cached data\n            window.location.href = '/auth/signin';\n        } catch (err) {\n            console.error('Sign out error:', err);\n            // Even if sign out fails, clear caches and redirect\n            try {\n                localStorage.clear();\n                sessionStorage.clear();\n            } catch (clearError) {\n                console.warn('Failed to clear storage on error:', clearError);\n            }\n            window.location.href = '/auth/signin';\n        }\n    };\n    // Handle search keyboard shortcut\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"Navbar.useEffect.handleKeyDown\": (e)=>{\n                    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {\n                        e.preventDefault();\n                        setIsSearchOpen(true);\n                    }\n                }\n            }[\"Navbar.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"Navbar.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"header border-b border-gray-200 bg-white/95 backdrop-blur-sm w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 sm:px-6 lg:px-8 \".concat(// When sidebar is expanded, use standard max width with centering\n                // When sidebar is collapsed, use full width with padding\n                isDesktop && (!isCollapsed || isHovered) ? 'max-w-7xl mx-auto' : isDesktop ? 'max-w-none' : 'max-w-7xl mx-auto'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleSidebar,\n                                    className: \"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\",\n                                    title: \"Toggle sidebar\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"RouKey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:flex items-center space-x-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: breadcrumb.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"/\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-900 font-medium\",\n                                            children: breadcrumb.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden xl:block relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsSearchOpen(true),\n                                            className: \"w-64 pl-10 pr-4 py-2.5 text-sm bg-white border border-gray-200 rounded-lg text-gray-500 hover:text-gray-700 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200 text-left flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Search...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                    className: \"hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium text-gray-400 bg-gray-100 border border-gray-200 rounded\",\n                                                    children: \"⌘K\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsSearchOpen(true),\n                                    className: \"xl:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:block relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsSettingsOpen(!isSettingsOpen),\n                                            className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-3 w-3 text-gray-600 transition-transform duration-200 \".concat(isSettingsOpen ? 'rotate-180' : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        isSettingsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"fixed inset-0 z-10\",\n                                                    onClick: ()=>setIsSettingsOpen(false)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: \"/settings\",\n                                                            className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200\",\n                                                            onClick: ()=>setIsSettingsOpen(false),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-3 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Account Settings\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: \"/billing\",\n                                                            className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200\",\n                                                            onClick: ()=>setIsSettingsOpen(false),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-3 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Billing & Plans\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                            className: \"my-1 border-gray-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleSignOut,\n                                                            className: \"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_CreditCardIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-3 text-red-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Sign Out\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-semibold text-sm\",\n                                                children: initials\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: firstName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: planName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlobalSearch__WEBPACK_IMPORTED_MODULE_7__.GlobalSearch, {\n                isOpen: isSearchOpen,\n                onClose: ()=>setIsSearchOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(Navbar, \"qg12mikj1OkeCMynJkTVJsPcusg=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_3__.useSidebar,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__.useSubscription\n    ];\n});\n_c = Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Navbar.tsx\n"));

/***/ })

});