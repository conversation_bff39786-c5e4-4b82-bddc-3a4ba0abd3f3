"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useSubscription.ts":
/*!**************************************!*\
  !*** ./src/hooks/useSubscription.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSubscription: () => (/* binding */ useSubscription)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n\n\nfunction useSubscription() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [subscriptionStatus, setSubscriptionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [usageStatus, setUsageStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__.createSupabaseBrowserClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSubscription.useEffect\": ()=>{\n            // Get initial user (more secure than getSession)\n            const getUser = {\n                \"useSubscription.useEffect.getUser\": async ()=>{\n                    const { data: { user } } = await supabase.auth.getUser();\n                    setUser(user !== null && user !== void 0 ? user : null);\n                    if (user) {\n                        fetchSubscriptionStatus(user);\n                        fetchUsageStatus(user);\n                    } else {\n                        setLoading(false);\n                    }\n                }\n            }[\"useSubscription.useEffect.getUser\"];\n            getUser();\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"useSubscription.useEffect\": async (event, session)=>{\n                    console.log('Auth state change in useSubscription:', event, !!session);\n                    var _session_user;\n                    setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        // Clear any cached data when switching users\n                        setSubscriptionStatus(null);\n                        setUsageStatus(null);\n                        setLoading(true);\n                        // Clear user-specific cache entries\n                        try {\n                            const { globalCache } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_utils_advancedCache_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/utils/advancedCache */ \"(app-pages-browser)/./src/utils/advancedCache.ts\"));\n                            globalCache.invalidateByTags([\n                                'user',\n                                'subscription',\n                                'usage'\n                            ]);\n                            console.log('User-specific cache invalidated for new user');\n                        } catch (cacheError) {\n                            console.warn('Could not invalidate user cache:', cacheError);\n                        }\n                        // Fetch fresh data for the new user\n                        await Promise.all([\n                            fetchSubscriptionStatus(session.user),\n                            fetchUsageStatus(session.user)\n                        ]);\n                    } else {\n                        // User signed out - clear all data\n                        setSubscriptionStatus(null);\n                        setUsageStatus(null);\n                        setError(null);\n                        setLoading(false);\n                    }\n                }\n            }[\"useSubscription.useEffect\"]);\n            return ({\n                \"useSubscription.useEffect\": ()=>subscription.unsubscribe()\n            })[\"useSubscription.useEffect\"];\n        }\n    }[\"useSubscription.useEffect\"], []);\n    const fetchSubscriptionStatus = async (currentUser)=>{\n        try {\n            console.log('Fetching subscription status for user:', currentUser.id);\n            // Use cached fetch with user-specific tags\n            const { cachedFetch } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_utils_advancedCache_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/utils/advancedCache */ \"(app-pages-browser)/./src/utils/advancedCache.ts\"));\n            const data = await cachedFetch(\"/api/stripe/subscription-status?userId=\".concat(currentUser.id), {\n                cacheKey: \"subscription_status_\".concat(currentUser.id),\n                cacheTTL: 60000,\n                tags: [\n                    'user',\n                    'subscription',\n                    \"user_\".concat(currentUser.id)\n                ]\n            });\n            console.log('Subscription status data:', data);\n            setSubscriptionStatus(data);\n        } catch (err) {\n            console.error('Error fetching subscription status:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const fetchUsageStatus = async (currentUser)=>{\n        try {\n            const response = await fetch('/api/stripe/subscription-status', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userId: currentUser.id\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch usage status');\n            }\n            const data = await response.json();\n            setUsageStatus(data);\n        } catch (err) {\n            console.error('Error fetching usage status:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createCheckoutSession = async (tier)=>{\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const response = await fetch('/api/stripe/create-checkout-session', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                priceId: getPriceIdForTier(tier),\n                userId: user.id,\n                userEmail: user.email,\n                tier\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || 'Failed to create checkout session');\n        }\n        const { url } = await response.json();\n        window.location.href = url;\n    };\n    const openCustomerPortal = async ()=>{\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const response = await fetch('/api/stripe/customer-portal', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                userId: user.id\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || 'Failed to open customer portal');\n        }\n        const { url } = await response.json();\n        window.location.href = url;\n    };\n    const refreshStatus = ()=>{\n        if (user) {\n            setLoading(true);\n            fetchSubscriptionStatus(user);\n            fetchUsageStatus(user);\n        }\n    };\n    return {\n        subscriptionStatus,\n        usageStatus,\n        loading,\n        error,\n        createCheckoutSession,\n        openCustomerPortal,\n        refreshStatus,\n        isAuthenticated: !!user,\n        user\n    };\n}\nfunction getPriceIdForTier(tier) {\n    switch(tier){\n        case 'free':\n            return \"price_1RcfRzC97XFBBUvd9XqkXe3a\";\n        case 'starter':\n            return \"price_1RcfWSC97XFBBUvdYHXbBvQ6\";\n        case 'professional':\n            return \"price_1RcfViC97XFBBUvdxwixKUdg\";\n        case 'enterprise':\n            return \"price_1RaADDC97XFBBUvd7j6OPJj7\";\n        default:\n            throw new Error(\"Invalid tier: \".concat(tier));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSubscription.ts\n"));

/***/ })

});