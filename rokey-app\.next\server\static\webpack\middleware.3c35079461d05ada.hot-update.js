"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(req) {\n    let res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request: {\n            headers: req.headers\n        }\n    });\n    // Skip middleware for static files and Next.js internal routes\n    const pathname = req.nextUrl.pathname;\n    if (pathname.startsWith('/_next/static') || pathname.startsWith('/_next/image') || pathname.startsWith('/favicon.ico') || pathname.startsWith('/public/') || pathname.includes('.')) {\n        return res;\n    }\n    // Temporary bypass for development if Supabase connectivity issues\n    if ( true && process.env.BYPASS_AUTH_MIDDLEWARE === 'true') {\n        console.log('Middleware: Bypassing auth checks due to BYPASS_AUTH_MIDDLEWARE=true');\n        return res;\n    }\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return req.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n                req.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                req.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n    // Get the pathname (already defined above)\n    // Public routes that don't require authentication\n    const publicRoutes = [\n        '/',\n        '/pricing',\n        '/auth/signin',\n        '/auth/signup',\n        '/auth/callback',\n        '/auth/verify-email',\n        '/checkout',\n        '/api/stripe/webhooks'\n    ];\n    // API routes that don't require authentication\n    const publicApiRoutes = [\n        '/api/stripe/webhooks',\n        '/api/system-status',\n        '/api/debug',\n        '/api/pricing',\n        '/api/external'\n    ];\n    // Check if the route is public\n    const isPublicRoute = publicRoutes.some((route)=>pathname === route || pathname.startsWith(route));\n    const isPublicApiRoute = publicApiRoutes.some((route)=>pathname.startsWith(route));\n    // Allow public routes and API routes\n    if (isPublicRoute || isPublicApiRoute) {\n        return res;\n    }\n    // Get the user with error handling for network issues (more secure than getSession)\n    let session = null;\n    try {\n        const { data: { user } } = await supabase.auth.getUser();\n        // Create a session-like object for compatibility\n        session = user ? {\n            user\n        } : null;\n    } catch (error) {\n        console.error('Middleware: Failed to get session from Supabase:', error);\n        // If we can't connect to Supabase, allow the request to proceed\n        // This prevents the entire app from being blocked by network issues\n        return res;\n    }\n    // If no session and trying to access protected route, redirect to signin\n    if (!session) {\n        const redirectUrl = new URL('/auth/signin', req.url);\n        redirectUrl.searchParams.set('redirectTo', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    // For authenticated users, check if they're accessing dashboard routes\n    if (pathname.startsWith('/dashboard') || pathname.startsWith('/playground') || pathname.startsWith('/logs') || pathname.startsWith('/my-models') || pathname.startsWith('/api-keys') || pathname.startsWith('/configurations')) {\n        // Check subscription status for protected app routes\n        try {\n            const { data: profile, error: profileError } = await supabase.from('user_profiles').select('subscription_status, subscription_tier').eq('id', session.user.id).single();\n            if (profileError) {\n                console.error('Middleware: Error fetching user profile:', profileError);\n                // If we can't fetch profile due to network issues, allow access\n                return res;\n            }\n            // If no profile exists, create a default free profile for the user\n            if (!profile) {\n                console.log('Middleware: No profile found for user, creating default free profile:', session.user.id);\n                try {\n                    const { error: createError } = await supabase.from('user_profiles').insert({\n                        id: session.user.id,\n                        full_name: session.user.user_metadata?.full_name || '',\n                        subscription_tier: 'free',\n                        subscription_status: 'active',\n                        created_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString()\n                    });\n                    if (createError) {\n                        console.error('Middleware: Error creating user profile:', createError);\n                        // If we can't create profile, redirect to pricing to be safe\n                        const redirectUrl = new URL('/pricing', req.url);\n                        redirectUrl.searchParams.set('checkout', 'true');\n                        redirectUrl.searchParams.set('message', 'profile_creation_failed');\n                        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                    }\n                    // Profile created successfully, allow access\n                    console.log('Middleware: Successfully created free profile for user:', session.user.id);\n                    return res;\n                } catch (error) {\n                    console.error('Middleware: Exception creating user profile:', error);\n                    const redirectUrl = new URL('/pricing', req.url);\n                    redirectUrl.searchParams.set('checkout', 'true');\n                    redirectUrl.searchParams.set('message', 'profile_creation_failed');\n                    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n                }\n            }\n            // Check subscription status - free tier users should always have access\n            // For free tier, we don't require subscription_status to be 'active' since they don't have paid subscriptions\n            const hasActiveSubscription = profile.subscription_status === 'active' || profile.subscription_tier === 'free';\n            if (!hasActiveSubscription) {\n                const redirectUrl = new URL('/pricing', req.url);\n                redirectUrl.searchParams.set('checkout', 'true');\n                redirectUrl.searchParams.set('message', 'subscription_required');\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n            }\n        } catch (error) {\n            console.error('Error checking subscription status in middleware:', error);\n            // On error, redirect to pricing to be safe\n            const redirectUrl = new URL('/pricing', req.url);\n            redirectUrl.searchParams.set('checkout', 'true');\n            redirectUrl.searchParams.set('message', 'subscription_check_failed');\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n    }\n    return res;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ '/((?!_next/static|_next/image|favicon.ico|public/).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});