"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(req) {\n    let res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request: {\n            headers: req.headers\n        }\n    });\n    // Skip middleware for static files and Next.js internal routes\n    const pathname = req.nextUrl.pathname;\n    if (pathname.startsWith('/_next/static') || pathname.startsWith('/_next/image') || pathname.startsWith('/favicon.ico') || pathname.startsWith('/public/') || pathname.includes('.')) {\n        return res;\n    }\n    // Temporary bypass for development if Supabase connectivity issues\n    if ( true && process.env.BYPASS_AUTH_MIDDLEWARE === 'true') {\n        console.log('Middleware: Bypassing auth checks due to BYPASS_AUTH_MIDDLEWARE=true');\n        return res;\n    }\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return req.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n                req.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                req.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n                res = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: req.headers\n                    }\n                });\n                res.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n    // Get the pathname (already defined above)\n    // Public routes that don't require authentication\n    const publicRoutes = [\n        '/',\n        '/pricing',\n        '/auth/signin',\n        '/auth/signup',\n        '/auth/callback',\n        '/auth/verify-email',\n        '/checkout',\n        '/api/stripe/webhooks'\n    ];\n    // API routes that don't require authentication\n    const publicApiRoutes = [\n        '/api/stripe/webhooks',\n        '/api/system-status',\n        '/api/debug',\n        '/api/pricing',\n        '/api/external'\n    ];\n    // Check if the route is public\n    const isPublicRoute = publicRoutes.some((route)=>pathname === route || pathname.startsWith(route));\n    const isPublicApiRoute = publicApiRoutes.some((route)=>pathname.startsWith(route));\n    // Allow public routes and API routes\n    if (isPublicRoute || isPublicApiRoute) {\n        return res;\n    }\n    // Get the user with error handling for network issues (more secure than getSession)\n    let session = null;\n    try {\n        const { data: { user } } = await supabase.auth.getUser();\n        // Create a session-like object for compatibility\n        session = user ? {\n            user\n        } : null;\n    } catch (error) {\n        console.error('Middleware: Failed to get session from Supabase:', error);\n        // If we can't connect to Supabase, allow the request to proceed\n        // This prevents the entire app from being blocked by network issues\n        return res;\n    }\n    // If no session and trying to access protected route, redirect to signin\n    if (!session) {\n        const redirectUrl = new URL('/auth/signin', req.url);\n        redirectUrl.searchParams.set('redirectTo', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    // For authenticated users, check if they're accessing dashboard routes\n    if (pathname.startsWith('/dashboard') || pathname.startsWith('/playground') || pathname.startsWith('/logs') || pathname.startsWith('/my-models') || pathname.startsWith('/api-keys') || pathname.startsWith('/configurations')) {\n        // Check subscription status for protected app routes\n        try {\n            const { data: profile, error: profileError } = await supabase.from('user_profiles').select('subscription_status, subscription_tier').eq('id', session.user.id).single();\n            if (profileError) {\n                console.error('Middleware: Error fetching user profile:', profileError);\n                // If we can't fetch profile due to network issues, allow access\n                return res;\n            }\n            // If no profile, redirect to pricing\n            if (!profile) {\n                const redirectUrl = new URL('/pricing', req.url);\n                redirectUrl.searchParams.set('checkout', 'true');\n                redirectUrl.searchParams.set('message', 'subscription_required');\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n            }\n            // Check subscription status - free tier users should always have access\n            // For free tier, we don't require subscription_status to be 'active' since they don't have paid subscriptions\n            const hasActiveSubscription = profile.subscription_status === 'active' || profile.subscription_tier === 'free';\n            if (!hasActiveSubscription) {\n                const redirectUrl = new URL('/pricing', req.url);\n                redirectUrl.searchParams.set('checkout', 'true');\n                redirectUrl.searchParams.set('message', 'subscription_required');\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n            }\n        } catch (error) {\n            console.error('Error checking subscription status in middleware:', error);\n            // On error, redirect to pricing to be safe\n            const redirectUrl = new URL('/pricing', req.url);\n            redirectUrl.searchParams.set('checkout', 'true');\n            redirectUrl.searchParams.set('message', 'subscription_check_failed');\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n    }\n    return res;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ '/((?!_next/static|_next/image|favicon.ico|public/).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});