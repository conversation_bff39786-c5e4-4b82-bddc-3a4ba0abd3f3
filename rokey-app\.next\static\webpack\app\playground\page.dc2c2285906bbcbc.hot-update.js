"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useSubscription.ts":
/*!**************************************!*\
  !*** ./src/hooks/useSubscription.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSubscription: () => (/* binding */ useSubscription)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n\n\nfunction useSubscription() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [subscriptionStatus, setSubscriptionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [usageStatus, setUsageStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__.createSupabaseBrowserClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSubscription.useEffect\": ()=>{\n            // Get initial user (more secure than getSession)\n            const getUser = {\n                \"useSubscription.useEffect.getUser\": async ()=>{\n                    const { data: { user } } = await supabase.auth.getUser();\n                    setUser(user !== null && user !== void 0 ? user : null);\n                    if (user) {\n                        fetchSubscriptionStatus(user);\n                        fetchUsageStatus(user);\n                    } else {\n                        setLoading(false);\n                    }\n                }\n            }[\"useSubscription.useEffect.getUser\"];\n            getUser();\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"useSubscription.useEffect\": async (event, session)=>{\n                    console.log('Auth state change in useSubscription:', event, !!session);\n                    var _session_user;\n                    setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        // Clear any cached data when switching users\n                        setSubscriptionStatus(null);\n                        setUsageStatus(null);\n                        setLoading(true);\n                        // Clear user-specific cache entries\n                        try {\n                            const { clearUserSpecificCache } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_utils_clearUserCache_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/utils/clearUserCache */ \"(app-pages-browser)/./src/utils/clearUserCache.ts\"));\n                            await clearUserSpecificCache(session.user.id);\n                            console.log('User-specific cache cleared for new user:', session.user.id);\n                        } catch (cacheError) {\n                            console.warn('Could not clear user cache:', cacheError);\n                        }\n                        // Small delay to ensure cache clearing is complete\n                        await new Promise({\n                            \"useSubscription.useEffect\": (resolve)=>setTimeout(resolve, 100)\n                        }[\"useSubscription.useEffect\"]);\n                        // Fetch fresh data for the new user\n                        await Promise.all([\n                            fetchSubscriptionStatus(session.user),\n                            fetchUsageStatus(session.user)\n                        ]);\n                    } else {\n                        // User signed out - clear all data\n                        setSubscriptionStatus(null);\n                        setUsageStatus(null);\n                        setError(null);\n                        setLoading(false);\n                    }\n                }\n            }[\"useSubscription.useEffect\"]);\n            return ({\n                \"useSubscription.useEffect\": ()=>subscription.unsubscribe()\n            })[\"useSubscription.useEffect\"];\n        }\n    }[\"useSubscription.useEffect\"], []);\n    const fetchSubscriptionStatus = async (currentUser)=>{\n        try {\n            console.log('Fetching subscription status for user:', currentUser.id);\n            // Force fresh data by adding cache-busting timestamp\n            const timestamp = Date.now();\n            const response = await fetch(\"/api/stripe/subscription-status?userId=\".concat(currentUser.id, \"&_t=\").concat(timestamp), {\n                cache: 'no-store',\n                headers: {\n                    'Cache-Control': 'no-cache, no-store, must-revalidate',\n                    'Pragma': 'no-cache',\n                    'Expires': '0'\n                }\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Subscription status API error:', {\n                    status: response.status,\n                    statusText: response.statusText,\n                    errorText\n                });\n                throw new Error(\"Failed to fetch subscription status: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log('Subscription status data:', data);\n            setSubscriptionStatus(data);\n        } catch (err) {\n            console.error('Error fetching subscription status:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        }\n    };\n    const fetchUsageStatus = async (currentUser)=>{\n        try {\n            // Force fresh data with cache-busting headers\n            const response = await fetch('/api/stripe/subscription-status', {\n                method: 'POST',\n                cache: 'no-store',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Cache-Control': 'no-cache, no-store, must-revalidate',\n                    'Pragma': 'no-cache',\n                    'Expires': '0'\n                },\n                body: JSON.stringify({\n                    userId: currentUser.id,\n                    _t: Date.now() // Cache buster\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch usage status');\n            }\n            const data = await response.json();\n            console.log('Usage status data:', data);\n            setUsageStatus(data);\n        } catch (err) {\n            console.error('Error fetching usage status:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createCheckoutSession = async (tier)=>{\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const response = await fetch('/api/stripe/create-checkout-session', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                priceId: getPriceIdForTier(tier),\n                userId: user.id,\n                userEmail: user.email,\n                tier\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || 'Failed to create checkout session');\n        }\n        const { url } = await response.json();\n        window.location.href = url;\n    };\n    const openCustomerPortal = async ()=>{\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const response = await fetch('/api/stripe/customer-portal', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                userId: user.id\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || 'Failed to open customer portal');\n        }\n        const { url } = await response.json();\n        window.location.href = url;\n    };\n    const refreshStatus = ()=>{\n        if (user) {\n            setLoading(true);\n            fetchSubscriptionStatus(user);\n            fetchUsageStatus(user);\n        }\n    };\n    return {\n        subscriptionStatus,\n        usageStatus,\n        loading,\n        error,\n        createCheckoutSession,\n        openCustomerPortal,\n        refreshStatus,\n        isAuthenticated: !!user,\n        user\n    };\n}\nfunction getPriceIdForTier(tier) {\n    switch(tier){\n        case 'free':\n            return \"price_1RcfRzC97XFBBUvd9XqkXe3a\";\n        case 'starter':\n            return \"price_1RcfWSC97XFBBUvdYHXbBvQ6\";\n        case 'professional':\n            return \"price_1RcfViC97XFBBUvdxwixKUdg\";\n        case 'enterprise':\n            return \"price_1RaADDC97XFBBUvd7j6OPJj7\";\n        default:\n            throw new Error(\"Invalid tier: \".concat(tier));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VTdWJzY3JpcHRpb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE0QztBQUN3QjtBQUk3RCxTQUFTRztJQUNkLE1BQU0sQ0FBQ0MsTUFBTUMsUUFBUSxHQUFHTCwrQ0FBUUEsQ0FBYztJQUM5QyxNQUFNLENBQUNNLG9CQUFvQkMsc0JBQXNCLEdBQUdQLCtDQUFRQSxDQUE0QjtJQUN4RixNQUFNLENBQUNRLGFBQWFDLGVBQWUsR0FBR1QsK0NBQVFBLENBQXFCO0lBQ25FLE1BQU0sQ0FBQ1UsU0FBU0MsV0FBVyxHQUFHWCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNZLE9BQU9DLFNBQVMsR0FBR2IsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU1jLFdBQVdaLGlGQUEyQkE7SUFFNUNELGdEQUFTQTtxQ0FBQztZQUNSLGlEQUFpRDtZQUNqRCxNQUFNYztxREFBVTtvQkFDZCxNQUFNLEVBQUVDLE1BQU0sRUFBRVosSUFBSSxFQUFFLEVBQUUsR0FBRyxNQUFNVSxTQUFTRyxJQUFJLENBQUNGLE9BQU87b0JBQ3REVixRQUFRRCxpQkFBQUEsa0JBQUFBLE9BQVE7b0JBRWhCLElBQUlBLE1BQU07d0JBQ1JjLHdCQUF3QmQ7d0JBQ3hCZSxpQkFBaUJmO29CQUNuQixPQUFPO3dCQUNMTyxXQUFXO29CQUNiO2dCQUNGOztZQUVBSTtZQUVBLDBCQUEwQjtZQUMxQixNQUFNLEVBQUVDLE1BQU0sRUFBRUksWUFBWSxFQUFFLEVBQUUsR0FBR04sU0FBU0csSUFBSSxDQUFDSSxpQkFBaUI7NkNBQ2hFLE9BQU9DLE9BQU9DO29CQUNaQyxRQUFRQyxHQUFHLENBQUMseUNBQXlDSCxPQUFPLENBQUMsQ0FBQ0M7d0JBQ3REQTtvQkFBUmxCLFFBQVFrQixDQUFBQSxnQkFBQUEsb0JBQUFBLDhCQUFBQSxRQUFTbkIsSUFBSSxjQUFibUIsMkJBQUFBLGdCQUFpQjtvQkFFekIsSUFBSUEsb0JBQUFBLDhCQUFBQSxRQUFTbkIsSUFBSSxFQUFFO3dCQUNqQiw2Q0FBNkM7d0JBQzdDRyxzQkFBc0I7d0JBQ3RCRSxlQUFlO3dCQUNmRSxXQUFXO3dCQUVYLG9DQUFvQzt3QkFDcEMsSUFBSTs0QkFDRixNQUFNLEVBQUVlLHNCQUFzQixFQUFFLEdBQUcsTUFBTSw4TkFBZ0M7NEJBQ3pFLE1BQU1BLHVCQUF1QkgsUUFBUW5CLElBQUksQ0FBQ3VCLEVBQUU7NEJBQzVDSCxRQUFRQyxHQUFHLENBQUMsNkNBQTZDRixRQUFRbkIsSUFBSSxDQUFDdUIsRUFBRTt3QkFDMUUsRUFBRSxPQUFPQyxZQUFZOzRCQUNuQkosUUFBUUssSUFBSSxDQUFDLCtCQUErQkQ7d0JBQzlDO3dCQUVBLG1EQUFtRDt3QkFDbkQsTUFBTSxJQUFJRTt5REFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUzs7d0JBRWpELG9DQUFvQzt3QkFDcEMsTUFBTUQsUUFBUUcsR0FBRyxDQUFDOzRCQUNoQmYsd0JBQXdCSyxRQUFRbkIsSUFBSTs0QkFDcENlLGlCQUFpQkksUUFBUW5CLElBQUk7eUJBQzlCO29CQUNILE9BQU87d0JBQ0wsbUNBQW1DO3dCQUNuQ0csc0JBQXNCO3dCQUN0QkUsZUFBZTt3QkFDZkksU0FBUzt3QkFDVEYsV0FBVztvQkFDYjtnQkFDRjs7WUFHRjs2Q0FBTyxJQUFNUyxhQUFhYyxXQUFXOztRQUN2QztvQ0FBRyxFQUFFO0lBRUwsTUFBTWhCLDBCQUEwQixPQUFPaUI7UUFDckMsSUFBSTtZQUNGWCxRQUFRQyxHQUFHLENBQUMsMENBQTBDVSxZQUFZUixFQUFFO1lBRXBFLHFEQUFxRDtZQUNyRCxNQUFNUyxZQUFZQyxLQUFLQyxHQUFHO1lBQzFCLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSwwQ0FBK0RKLE9BQXJCRCxZQUFZUixFQUFFLEVBQUMsUUFBZ0IsT0FBVlMsWUFBYTtnQkFDdkdLLE9BQU87Z0JBQ1BDLFNBQVM7b0JBQ1AsaUJBQWlCO29CQUNqQixVQUFVO29CQUNWLFdBQVc7Z0JBQ2I7WUFDRjtZQUVBLElBQUksQ0FBQ0gsU0FBU0ksRUFBRSxFQUFFO2dCQUNoQixNQUFNQyxZQUFZLE1BQU1MLFNBQVNNLElBQUk7Z0JBQ3JDckIsUUFBUVosS0FBSyxDQUFDLGtDQUFrQztvQkFDOUNrQyxRQUFRUCxTQUFTTyxNQUFNO29CQUN2QkMsWUFBWVIsU0FBU1EsVUFBVTtvQkFDL0JIO2dCQUNGO2dCQUNBLE1BQU0sSUFBSUksTUFBTSx3Q0FBMkRULE9BQW5CQSxTQUFTTyxNQUFNLEVBQUMsS0FBdUIsT0FBcEJQLFNBQVNRLFVBQVU7WUFDaEc7WUFFQSxNQUFNL0IsT0FBTyxNQUFNdUIsU0FBU1UsSUFBSTtZQUNoQ3pCLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkJUO1lBQ3pDVCxzQkFBc0JTO1FBQ3hCLEVBQUUsT0FBT2tDLEtBQUs7WUFDWjFCLFFBQVFaLEtBQUssQ0FBQyx1Q0FBdUNzQztZQUNyRHJDLFNBQVNxQyxlQUFlRixRQUFRRSxJQUFJQyxPQUFPLEdBQUc7UUFDaEQ7SUFDRjtJQUVBLE1BQU1oQyxtQkFBbUIsT0FBT2dCO1FBQzlCLElBQUk7WUFDRiw4Q0FBOEM7WUFDOUMsTUFBTUksV0FBVyxNQUFNQyxNQUFNLG1DQUFtQztnQkFDOURZLFFBQVE7Z0JBQ1JYLE9BQU87Z0JBQ1BDLFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQixpQkFBaUI7b0JBQ2pCLFVBQVU7b0JBQ1YsV0FBVztnQkFDYjtnQkFDQVcsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQkMsUUFBUXJCLFlBQVlSLEVBQUU7b0JBQ3RCOEIsSUFBSXBCLEtBQUtDLEdBQUcsR0FBRyxlQUFlO2dCQUNoQztZQUNGO1lBRUEsSUFBSSxDQUFDQyxTQUFTSSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUssTUFBTTtZQUNsQjtZQUVBLE1BQU1oQyxPQUFPLE1BQU11QixTQUFTVSxJQUFJO1lBQ2hDekIsUUFBUUMsR0FBRyxDQUFDLHNCQUFzQlQ7WUFDbENQLGVBQWVPO1FBQ2pCLEVBQUUsT0FBT2tDLEtBQUs7WUFDWjFCLFFBQVFaLEtBQUssQ0FBQyxnQ0FBZ0NzQztZQUM5Q3JDLFNBQVNxQyxlQUFlRixRQUFRRSxJQUFJQyxPQUFPLEdBQUc7UUFDaEQsU0FBVTtZQUNSeEMsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNK0Msd0JBQXdCLE9BQU9DO1FBQ25DLElBQUksQ0FBQ3ZELE1BQU07WUFDVCxNQUFNLElBQUk0QyxNQUFNO1FBQ2xCO1FBRUEsTUFBTVQsV0FBVyxNQUFNQyxNQUFNLHVDQUF1QztZQUNsRVksUUFBUTtZQUNSVixTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtZQUNBVyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7Z0JBQ25CSyxTQUFTQyxrQkFBa0JGO2dCQUMzQkgsUUFBUXBELEtBQUt1QixFQUFFO2dCQUNmbUMsV0FBVzFELEtBQUsyRCxLQUFLO2dCQUNyQko7WUFDRjtRQUNGO1FBRUEsSUFBSSxDQUFDcEIsU0FBU0ksRUFBRSxFQUFFO1lBQ2hCLE1BQU1xQixZQUFZLE1BQU16QixTQUFTVSxJQUFJO1lBQ3JDLE1BQU0sSUFBSUQsTUFBTWdCLFVBQVVwRCxLQUFLLElBQUk7UUFDckM7UUFFQSxNQUFNLEVBQUVxRCxHQUFHLEVBQUUsR0FBRyxNQUFNMUIsU0FBU1UsSUFBSTtRQUNuQ2lCLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHSDtJQUN6QjtJQUVBLE1BQU1JLHFCQUFxQjtRQUN6QixJQUFJLENBQUNqRSxNQUFNO1lBQ1QsTUFBTSxJQUFJNEMsTUFBTTtRQUNsQjtRQUVBLE1BQU1ULFdBQVcsTUFBTUMsTUFBTSwrQkFBK0I7WUFDMURZLFFBQVE7WUFDUlYsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7WUFDQVcsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO2dCQUNuQkMsUUFBUXBELEtBQUt1QixFQUFFO1lBQ2pCO1FBQ0Y7UUFFQSxJQUFJLENBQUNZLFNBQVNJLEVBQUUsRUFBRTtZQUNoQixNQUFNcUIsWUFBWSxNQUFNekIsU0FBU1UsSUFBSTtZQUNyQyxNQUFNLElBQUlELE1BQU1nQixVQUFVcEQsS0FBSyxJQUFJO1FBQ3JDO1FBRUEsTUFBTSxFQUFFcUQsR0FBRyxFQUFFLEdBQUcsTUFBTTFCLFNBQVNVLElBQUk7UUFDbkNpQixPQUFPQyxRQUFRLENBQUNDLElBQUksR0FBR0g7SUFDekI7SUFFQSxNQUFNSyxnQkFBZ0I7UUFDcEIsSUFBSWxFLE1BQU07WUFDUk8sV0FBVztZQUNYTyx3QkFBd0JkO1lBQ3hCZSxpQkFBaUJmO1FBQ25CO0lBQ0Y7SUFFQSxPQUFPO1FBQ0xFO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBQ0E4QztRQUNBVztRQUNBQztRQUNBQyxpQkFBaUIsQ0FBQyxDQUFDbkU7UUFDbkJBO0lBQ0Y7QUFDRjtBQUVBLFNBQVN5RCxrQkFBa0JGLElBQXNCO0lBQy9DLE9BQVFBO1FBQ04sS0FBSztZQUNILE9BQU9hLGdDQUE0QztRQUNyRCxLQUFLO1lBQ0gsT0FBT0EsZ0NBQStDO1FBQ3hELEtBQUs7WUFDSCxPQUFPQSxnQ0FBb0Q7UUFDN0QsS0FBSztZQUNILE9BQU9BLGdDQUFrRDtRQUMzRDtZQUNFLE1BQU0sSUFBSXhCLE1BQU0saUJBQXNCLE9BQUxXO0lBQ3JDO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxob29rc1xcdXNlU3Vic2NyaXB0aW9uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBjcmVhdGVTdXBhYmFzZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAL2xpYi9zdXBhYmFzZS9jbGllbnQnO1xuaW1wb3J0IHsgU3Vic2NyaXB0aW9uU3RhdHVzLCBVc2FnZVN0YXR1cywgU3Vic2NyaXB0aW9uVGllciB9IGZyb20gJ0AvbGliL3N0cmlwZS1jbGllbnQnO1xuaW1wb3J0IHsgVXNlciB9IGZyb20gJ0BzdXBhYmFzZS9zdXBhYmFzZS1qcyc7XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VTdWJzY3JpcHRpb24oKSB7XG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3N1YnNjcmlwdGlvblN0YXR1cywgc2V0U3Vic2NyaXB0aW9uU3RhdHVzXSA9IHVzZVN0YXRlPFN1YnNjcmlwdGlvblN0YXR1cyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbdXNhZ2VTdGF0dXMsIHNldFVzYWdlU3RhdHVzXSA9IHVzZVN0YXRlPFVzYWdlU3RhdHVzIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZVN1cGFiYXNlQnJvd3NlckNsaWVudCgpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gR2V0IGluaXRpYWwgdXNlciAobW9yZSBzZWN1cmUgdGhhbiBnZXRTZXNzaW9uKVxuICAgIGNvbnN0IGdldFVzZXIgPSBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCB7IGRhdGE6IHsgdXNlciB9IH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcbiAgICAgIHNldFVzZXIodXNlciA/PyBudWxsKTtcblxuICAgICAgaWYgKHVzZXIpIHtcbiAgICAgICAgZmV0Y2hTdWJzY3JpcHRpb25TdGF0dXModXNlcik7XG4gICAgICAgIGZldGNoVXNhZ2VTdGF0dXModXNlcik7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgZ2V0VXNlcigpO1xuXG4gICAgLy8gTGlzdGVuIGZvciBhdXRoIGNoYW5nZXNcbiAgICBjb25zdCB7IGRhdGE6IHsgc3Vic2NyaXB0aW9uIH0gfSA9IHN1cGFiYXNlLmF1dGgub25BdXRoU3RhdGVDaGFuZ2UoXG4gICAgICBhc3luYyAoZXZlbnQsIHNlc3Npb24pID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coJ0F1dGggc3RhdGUgY2hhbmdlIGluIHVzZVN1YnNjcmlwdGlvbjonLCBldmVudCwgISFzZXNzaW9uKTtcbiAgICAgICAgc2V0VXNlcihzZXNzaW9uPy51c2VyID8/IG51bGwpO1xuXG4gICAgICAgIGlmIChzZXNzaW9uPy51c2VyKSB7XG4gICAgICAgICAgLy8gQ2xlYXIgYW55IGNhY2hlZCBkYXRhIHdoZW4gc3dpdGNoaW5nIHVzZXJzXG4gICAgICAgICAgc2V0U3Vic2NyaXB0aW9uU3RhdHVzKG51bGwpO1xuICAgICAgICAgIHNldFVzYWdlU3RhdHVzKG51bGwpO1xuICAgICAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG5cbiAgICAgICAgICAvLyBDbGVhciB1c2VyLXNwZWNpZmljIGNhY2hlIGVudHJpZXNcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgeyBjbGVhclVzZXJTcGVjaWZpY0NhY2hlIH0gPSBhd2FpdCBpbXBvcnQoJ0AvdXRpbHMvY2xlYXJVc2VyQ2FjaGUnKTtcbiAgICAgICAgICAgIGF3YWl0IGNsZWFyVXNlclNwZWNpZmljQ2FjaGUoc2Vzc2lvbi51c2VyLmlkKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdVc2VyLXNwZWNpZmljIGNhY2hlIGNsZWFyZWQgZm9yIG5ldyB1c2VyOicsIHNlc3Npb24udXNlci5pZCk7XG4gICAgICAgICAgfSBjYXRjaCAoY2FjaGVFcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKCdDb3VsZCBub3QgY2xlYXIgdXNlciBjYWNoZTonLCBjYWNoZUVycm9yKTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBTbWFsbCBkZWxheSB0byBlbnN1cmUgY2FjaGUgY2xlYXJpbmcgaXMgY29tcGxldGVcbiAgICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwKSk7XG5cbiAgICAgICAgICAvLyBGZXRjaCBmcmVzaCBkYXRhIGZvciB0aGUgbmV3IHVzZXJcbiAgICAgICAgICBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICAgICAgICBmZXRjaFN1YnNjcmlwdGlvblN0YXR1cyhzZXNzaW9uLnVzZXIpLFxuICAgICAgICAgICAgZmV0Y2hVc2FnZVN0YXR1cyhzZXNzaW9uLnVzZXIpXG4gICAgICAgICAgXSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gVXNlciBzaWduZWQgb3V0IC0gY2xlYXIgYWxsIGRhdGFcbiAgICAgICAgICBzZXRTdWJzY3JpcHRpb25TdGF0dXMobnVsbCk7XG4gICAgICAgICAgc2V0VXNhZ2VTdGF0dXMobnVsbCk7XG4gICAgICAgICAgc2V0RXJyb3IobnVsbCk7XG4gICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICApO1xuXG4gICAgcmV0dXJuICgpID0+IHN1YnNjcmlwdGlvbi51bnN1YnNjcmliZSgpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgZmV0Y2hTdWJzY3JpcHRpb25TdGF0dXMgPSBhc3luYyAoY3VycmVudFVzZXI6IFVzZXIpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ0ZldGNoaW5nIHN1YnNjcmlwdGlvbiBzdGF0dXMgZm9yIHVzZXI6JywgY3VycmVudFVzZXIuaWQpO1xuXG4gICAgICAvLyBGb3JjZSBmcmVzaCBkYXRhIGJ5IGFkZGluZyBjYWNoZS1idXN0aW5nIHRpbWVzdGFtcFxuICAgICAgY29uc3QgdGltZXN0YW1wID0gRGF0ZS5ub3coKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvc3RyaXBlL3N1YnNjcmlwdGlvbi1zdGF0dXM/dXNlcklkPSR7Y3VycmVudFVzZXIuaWR9Jl90PSR7dGltZXN0YW1wfWAsIHtcbiAgICAgICAgY2FjaGU6ICduby1zdG9yZScsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ2FjaGUtQ29udHJvbCc6ICduby1jYWNoZSwgbm8tc3RvcmUsIG11c3QtcmV2YWxpZGF0ZScsXG4gICAgICAgICAgJ1ByYWdtYSc6ICduby1jYWNoZScsXG4gICAgICAgICAgJ0V4cGlyZXMnOiAnMCdcbiAgICAgICAgfVxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCdTdWJzY3JpcHRpb24gc3RhdHVzIEFQSSBlcnJvcjonLCB7XG4gICAgICAgICAgc3RhdHVzOiByZXNwb25zZS5zdGF0dXMsXG4gICAgICAgICAgc3RhdHVzVGV4dDogcmVzcG9uc2Uuc3RhdHVzVGV4dCxcbiAgICAgICAgICBlcnJvclRleHRcbiAgICAgICAgfSk7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIHN1YnNjcmlwdGlvbiBzdGF0dXM6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBjb25zb2xlLmxvZygnU3Vic2NyaXB0aW9uIHN0YXR1cyBkYXRhOicsIGRhdGEpO1xuICAgICAgc2V0U3Vic2NyaXB0aW9uU3RhdHVzKGRhdGEpO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgc3Vic2NyaXB0aW9uIHN0YXR1czonLCBlcnIpO1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZldGNoVXNhZ2VTdGF0dXMgPSBhc3luYyAoY3VycmVudFVzZXI6IFVzZXIpID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8gRm9yY2UgZnJlc2ggZGF0YSB3aXRoIGNhY2hlLWJ1c3RpbmcgaGVhZGVyc1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9zdHJpcGUvc3Vic2NyaXB0aW9uLXN0YXR1cycsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGNhY2hlOiAnbm8tc3RvcmUnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgICAnQ2FjaGUtQ29udHJvbCc6ICduby1jYWNoZSwgbm8tc3RvcmUsIG11c3QtcmV2YWxpZGF0ZScsXG4gICAgICAgICAgJ1ByYWdtYSc6ICduby1jYWNoZScsXG4gICAgICAgICAgJ0V4cGlyZXMnOiAnMCdcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIHVzZXJJZDogY3VycmVudFVzZXIuaWQsXG4gICAgICAgICAgX3Q6IERhdGUubm93KCkgLy8gQ2FjaGUgYnVzdGVyXG4gICAgICAgIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggdXNhZ2Ugc3RhdHVzJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBjb25zb2xlLmxvZygnVXNhZ2Ugc3RhdHVzIGRhdGE6JywgZGF0YSk7XG4gICAgICBzZXRVc2FnZVN0YXR1cyhkYXRhKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHVzYWdlIHN0YXR1czonLCBlcnIpO1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBjcmVhdGVDaGVja291dFNlc3Npb24gPSBhc3luYyAodGllcjogU3Vic2NyaXB0aW9uVGllcikgPT4ge1xuICAgIGlmICghdXNlcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdVc2VyIG5vdCBhdXRoZW50aWNhdGVkJyk7XG4gICAgfVxuXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9zdHJpcGUvY3JlYXRlLWNoZWNrb3V0LXNlc3Npb24nLCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIH0sXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgIHByaWNlSWQ6IGdldFByaWNlSWRGb3JUaWVyKHRpZXIpLFxuICAgICAgICB1c2VySWQ6IHVzZXIuaWQsXG4gICAgICAgIHVzZXJFbWFpbDogdXNlci5lbWFpbCxcbiAgICAgICAgdGllcixcbiAgICAgIH0pLFxuICAgIH0pO1xuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5lcnJvciB8fCAnRmFpbGVkIHRvIGNyZWF0ZSBjaGVja291dCBzZXNzaW9uJyk7XG4gICAgfVxuXG4gICAgY29uc3QgeyB1cmwgfSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IHVybDtcbiAgfTtcblxuICBjb25zdCBvcGVuQ3VzdG9tZXJQb3J0YWwgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKTtcbiAgICB9XG5cbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3N0cmlwZS9jdXN0b21lci1wb3J0YWwnLCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIH0sXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgIHVzZXJJZDogdXNlci5pZCxcbiAgICAgIH0pLFxuICAgIH0pO1xuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5lcnJvciB8fCAnRmFpbGVkIHRvIG9wZW4gY3VzdG9tZXIgcG9ydGFsJyk7XG4gICAgfVxuXG4gICAgY29uc3QgeyB1cmwgfSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IHVybDtcbiAgfTtcblxuICBjb25zdCByZWZyZXNoU3RhdHVzID0gKCkgPT4ge1xuICAgIGlmICh1c2VyKSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgZmV0Y2hTdWJzY3JpcHRpb25TdGF0dXModXNlcik7XG4gICAgICBmZXRjaFVzYWdlU3RhdHVzKHVzZXIpO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4ge1xuICAgIHN1YnNjcmlwdGlvblN0YXR1cyxcbiAgICB1c2FnZVN0YXR1cyxcbiAgICBsb2FkaW5nLFxuICAgIGVycm9yLFxuICAgIGNyZWF0ZUNoZWNrb3V0U2Vzc2lvbixcbiAgICBvcGVuQ3VzdG9tZXJQb3J0YWwsXG4gICAgcmVmcmVzaFN0YXR1cyxcbiAgICBpc0F1dGhlbnRpY2F0ZWQ6ICEhdXNlcixcbiAgICB1c2VyLFxuICB9O1xufVxuXG5mdW5jdGlvbiBnZXRQcmljZUlkRm9yVGllcih0aWVyOiBTdWJzY3JpcHRpb25UaWVyKTogc3RyaW5nIHtcbiAgc3dpdGNoICh0aWVyKSB7XG4gICAgY2FzZSAnZnJlZSc6XG4gICAgICByZXR1cm4gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1RSSVBFX0ZSRUVfUFJJQ0VfSUQhO1xuICAgIGNhc2UgJ3N0YXJ0ZXInOlxuICAgICAgcmV0dXJuIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NUUklQRV9TVEFSVEVSX1BSSUNFX0lEITtcbiAgICBjYXNlICdwcm9mZXNzaW9uYWwnOlxuICAgICAgcmV0dXJuIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NUUklQRV9QUk9GRVNTSU9OQUxfUFJJQ0VfSUQhO1xuICAgIGNhc2UgJ2VudGVycHJpc2UnOlxuICAgICAgcmV0dXJuIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NUUklQRV9FTlRFUlBSSVNFX1BSSUNFX0lEITtcbiAgICBkZWZhdWx0OlxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbnZhbGlkIHRpZXI6ICR7dGllcn1gKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiY3JlYXRlU3VwYWJhc2VCcm93c2VyQ2xpZW50IiwidXNlU3Vic2NyaXB0aW9uIiwidXNlciIsInNldFVzZXIiLCJzdWJzY3JpcHRpb25TdGF0dXMiLCJzZXRTdWJzY3JpcHRpb25TdGF0dXMiLCJ1c2FnZVN0YXR1cyIsInNldFVzYWdlU3RhdHVzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwic3VwYWJhc2UiLCJnZXRVc2VyIiwiZGF0YSIsImF1dGgiLCJmZXRjaFN1YnNjcmlwdGlvblN0YXR1cyIsImZldGNoVXNhZ2VTdGF0dXMiLCJzdWJzY3JpcHRpb24iLCJvbkF1dGhTdGF0ZUNoYW5nZSIsImV2ZW50Iiwic2Vzc2lvbiIsImNvbnNvbGUiLCJsb2ciLCJjbGVhclVzZXJTcGVjaWZpY0NhY2hlIiwiaWQiLCJjYWNoZUVycm9yIiwid2FybiIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImFsbCIsInVuc3Vic2NyaWJlIiwiY3VycmVudFVzZXIiLCJ0aW1lc3RhbXAiLCJEYXRlIiwibm93IiwicmVzcG9uc2UiLCJmZXRjaCIsImNhY2hlIiwiaGVhZGVycyIsIm9rIiwiZXJyb3JUZXh0IiwidGV4dCIsInN0YXR1cyIsInN0YXR1c1RleHQiLCJFcnJvciIsImpzb24iLCJlcnIiLCJtZXNzYWdlIiwibWV0aG9kIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJ1c2VySWQiLCJfdCIsImNyZWF0ZUNoZWNrb3V0U2Vzc2lvbiIsInRpZXIiLCJwcmljZUlkIiwiZ2V0UHJpY2VJZEZvclRpZXIiLCJ1c2VyRW1haWwiLCJlbWFpbCIsImVycm9yRGF0YSIsInVybCIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsIm9wZW5DdXN0b21lclBvcnRhbCIsInJlZnJlc2hTdGF0dXMiLCJpc0F1dGhlbnRpY2F0ZWQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1RSSVBFX0ZSRUVfUFJJQ0VfSUQiLCJORVhUX1BVQkxJQ19TVFJJUEVfU1RBUlRFUl9QUklDRV9JRCIsIk5FWFRfUFVCTElDX1NUUklQRV9QUk9GRVNTSU9OQUxfUFJJQ0VfSUQiLCJORVhUX1BVQkxJQ19TVFJJUEVfRU5URVJQUklTRV9QUklDRV9JRCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSubscription.ts\n"));

/***/ })

});