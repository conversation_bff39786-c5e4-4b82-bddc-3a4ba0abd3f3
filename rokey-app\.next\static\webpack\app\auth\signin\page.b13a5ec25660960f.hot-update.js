"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signin/page",{

/***/ "(app-pages-browser)/./src/app/auth/signin/page.tsx":
/*!**************************************!*\
  !*** ./src/app/auth/signin/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,EyeIcon,EyeSlashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,EyeIcon,EyeSlashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,EyeIcon,EyeSlashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,EyeIcon,EyeSlashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SignInPageContent() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Password reset modal state\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resetEmail, setResetEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isResetLoading, setIsResetLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resetError, setResetError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_5__.createSupabaseBrowserClient)();\n    const { success, error: toastError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SignInPageContent.useEffect\": ()=>{\n            // TEMPORARILY DISABLED: Check if user is already signed in\n            // This is causing redirect loops, so we'll let users manually fill out the form\n            console.log('Signin page loaded, automatic session check disabled to prevent redirect loops');\n        /*\n    const checkUser = async () => {\n      const { data: { session } } = await supabase.auth.getSession();\n      if (session) {\n        const redirectTo = searchParams.get('redirectTo');\n        const plan = searchParams.get('plan');\n        const checkoutUserId = searchParams.get('checkout_user_id');\n        const message = searchParams.get('message');\n\n        // Only redirect to checkout if this is explicitly a checkout flow\n        if (checkoutUserId && plan && ['starter', 'professional', 'enterprise'].includes(plan)) {\n          router.push(`/checkout?plan=${plan}&user_id=${session.user.id}`);\n        } else if (message === 'account_created' && plan && ['starter', 'professional', 'enterprise'].includes(plan)) {\n          router.push(`/checkout?plan=${plan}&user_id=${session.user.id}`);\n        } else if (redirectTo) {\n          router.push(redirectTo);\n        } else {\n          router.push('/dashboard');\n        }\n      }\n    };\n    checkUser();\n    */ }\n    }[\"SignInPageContent.useEffect\"], [\n        router,\n        searchParams,\n        supabase.auth\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError('');\n        try {\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                throw error;\n            }\n            if (data.user) {\n                console.log('Sign in successful, user:', data.user.id);\n                console.log('Current URL:', window.location.href);\n                console.log('Environment:', \"development\");\n                console.log('Site URL:', \"http://localhost:3000\");\n                // Wait a moment for session to be established\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                // Handle redirect logic - users should be able to access dashboard regardless of subscription\n                const redirectTo = searchParams.get('redirectTo');\n                const plan = searchParams.get('plan');\n                const email = searchParams.get('email');\n                const checkoutUserId = searchParams.get('checkout_user_id');\n                const message = searchParams.get('message');\n                console.log('Redirect params:', {\n                    redirectTo,\n                    plan,\n                    email,\n                    checkoutUserId,\n                    message\n                });\n                // Only redirect to checkout if this is explicitly a checkout flow\n                // This prevents free users from being redirected to checkout just because they have plan params in URL\n                if (checkoutUserId && plan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(plan)) {\n                    // This is a checkout flow - user was created but needs to complete payment\n                    const checkoutUrl = \"/checkout?plan=\".concat(plan, \"&user_id=\").concat(data.user.id).concat(email ? \"&email=\".concat(encodeURIComponent(email)) : '');\n                    console.log('Redirecting to checkout (checkout flow):', checkoutUrl);\n                    router.push(checkoutUrl);\n                } else if (message === 'account_created' && plan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(plan)) {\n                    // User just created account for paid plan and needs to complete checkout\n                    const checkoutUrl = \"/checkout?plan=\".concat(plan, \"&user_id=\").concat(data.user.id).concat(email ? \"&email=\".concat(encodeURIComponent(email)) : '');\n                    console.log('Redirecting to checkout (account created):', checkoutUrl);\n                    router.push(checkoutUrl);\n                } else if (redirectTo) {\n                    // Redirect to specified location\n                    console.log('Redirecting to specified location:', redirectTo);\n                    router.push(redirectTo);\n                } else {\n                    // Default redirect to dashboard - all users should be able to access dashboard\n                    // This includes free users and existing users who just want to sign in normally\n                    console.log('Redirecting to dashboard (default)');\n                    router.push('/dashboard');\n                }\n            }\n        } catch (err) {\n            console.error('Sign in error:', err);\n            setError(err.message || 'Invalid email or password. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handlePasswordReset = async (e)=>{\n        e.preventDefault();\n        setIsResetLoading(true);\n        setResetError('');\n        if (!resetEmail.trim()) {\n            setResetError('Please enter your email address');\n            setIsResetLoading(false);\n            return;\n        }\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(resetEmail, {\n                redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n            });\n            if (error) {\n                throw error;\n            }\n            success('Password reset email sent!', 'Check your inbox for instructions to reset your password.');\n            setShowResetModal(false);\n            setResetEmail('');\n        } catch (err) {\n            console.error('Password reset error:', err);\n            setResetError(err.message || 'Failed to send reset email. Please try again.');\n            toastError('Failed to send reset email', err.message || 'Please try again.');\n        } finally{\n            setIsResetLoading(false);\n        }\n    };\n    const openResetModal = ()=>{\n        setResetEmail(email); // Pre-fill with current email if available\n        setResetError('');\n        setShowResetModal(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                gridSize: 50,\n                opacity: 0.064,\n                color: \"#000000\",\n                variant: \"subtle\",\n                animated: true,\n                className: \"fixed inset-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: \"radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)\",\n                        backgroundSize: '100px 100px',\n                        mask: \"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)\",\n                        WebkitMask: \"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 w-full max-w-6xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"hidden lg:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 opacity-20\",\n                                        style: {\n                                            backgroundImage: \"\\n                    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                  \",\n                                            backgroundSize: '30px 30px'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            src: \"/roukey_logo.png\",\n                                                            alt: \"RouKey\",\n                                                            width: 48,\n                                                            height: 48,\n                                                            className: \"w-12 h-12 object-contain\",\n                                                            priority: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-4xl font-bold mb-4\",\n                                                        children: \"Welcome to RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-white/90 mb-8\",\n                                                        children: [\n                                                            \"Access to \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-bold\",\n                                                                children: \"UNLIMITED\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            \" AI requests across 300+ models\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/90\",\n                                                                children: \"Intelligent Role Routing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/90\",\n                                                                children: \"Enterprise Security\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/90\",\n                                                                children: \"No Request Limits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            className: \"w-full max-w-md mx-auto lg:mx-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"inline-flex items-center space-x-3 mb-8 justify-center lg:justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        src: \"/roukey_logo.png\",\n                                                        alt: \"RouKey\",\n                                                        width: 40,\n                                                        height: 40,\n                                                        className: \"w-full h-full object-contain\",\n                                                        priority: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold text-black\",\n                                                    children: \"RouKey\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold text-black mb-3\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-lg\",\n                                            children: \"Welcome back to your AI gateway\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-5\",\n                                            style: {\n                                                backgroundImage: \"\\n                    linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n                    linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n                  \",\n                                                backgroundSize: '20px 20px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10\",\n                                            children: [\n                                                searchParams.get('message') === 'account_created' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 p-4 bg-green-50 border border-green-200 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-600 text-sm font-medium\",\n                                                        children: \"✅ Account created successfully! Please sign in to complete your checkout.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, this),\n                                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-600 text-sm\",\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleSubmit,\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"email\",\n                                                                    className: \"block text-sm font-semibold text-gray-800 mb-3\",\n                                                                    children: \"\\uD83D\\uDCE7 Email Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"email\",\n                                                                    name: \"email\",\n                                                                    type: \"email\",\n                                                                    autoComplete: \"email\",\n                                                                    required: true,\n                                                                    value: email,\n                                                                    onChange: (e)=>setEmail(e.target.value),\n                                                                    className: \"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white\",\n                                                                    placeholder: \"Enter your email\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"password\",\n                                                                    className: \"block text-sm font-semibold text-gray-800 mb-3\",\n                                                                    children: \"\\uD83D\\uDD12 Password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"password\",\n                                                                            name: \"password\",\n                                                                            type: showPassword ? 'text' : 'password',\n                                                                            autoComplete: \"current-password\",\n                                                                            required: true,\n                                                                            value: password,\n                                                                            onChange: (e)=>setPassword(e.target.value),\n                                                                            className: \"w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white\",\n                                                                            placeholder: \"Enter your password\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                            lineNumber: 327,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                                            className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors\",\n                                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                                lineNumber: 344,\n                                                                                columnNumber: 27\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"remember-me\",\n                                                                            name: \"remember-me\",\n                                                                            type: \"checkbox\",\n                                                                            className: \"h-5 w-5 text-[#ff6b35] focus:ring-[#ff6b35] border-gray-300 rounded-lg\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                            lineNumber: 354,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"remember-me\",\n                                                                            className: \"ml-3 block text-sm font-medium text-gray-700\",\n                                                                            children: \"Remember me\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                            lineNumber: 360,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: openResetModal,\n                                                                    className: \"text-sm text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors\",\n                                                                    children: \"Forgot password?\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: isLoading,\n                                                            className: \"w-full bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-[#e55a2b] hover:to-[#e8851a] focus:ring-4 focus:ring-[#ff6b35]/30 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02]\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Signing in...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 23\n                                                            }, this) : 'Sign In'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-lg\",\n                                        children: [\n                                            \"Don't have an account?\",\n                                            ' ',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/auth/signup\",\n                                                className: \"text-[#ff6b35] hover:text-[#e55a2b] font-bold transition-colors\",\n                                                children: \"Sign up for free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this),\n            showResetModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-black bg-opacity-50 transition-opacity\",\n                            onClick: ()=>setShowResetModal(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white px-6 pt-6 pb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"Reset Your Password\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowResetModal(false),\n                                                className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6\",\n                                        children: \"Enter your email address and we'll send you a link to reset your password.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 17\n                                    }, this),\n                                    resetError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-600 text-sm\",\n                                            children: resetError\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handlePasswordReset,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"resetEmail\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Email Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"resetEmail\",\n                                                                type: \"email\",\n                                                                value: resetEmail,\n                                                                onChange: (e)=>setResetEmail(e.target.value),\n                                                                placeholder: \"Enter your email address\",\n                                                                className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent transition-all duration-200\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowResetModal(false),\n                                                        className: \"flex-1 px-4 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        disabled: isResetLoading,\n                                                        className: \"flex-1 px-4 py-3 text-sm font-medium text-white bg-[#ff6b35] border border-transparent rounded-xl hover:bg-[#e55a2b] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#ff6b35] disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                        children: isResetLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Sending...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 25\n                                                        }, this) : 'Send Reset Link'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 411,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n_s(SignInPageContent, \"EZp0z/+fgwBvgm/VvUKXKXxOpS8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams,\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = SignInPageContent;\nfunction SignInPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                        lineNumber: 500,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 498,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n            lineNumber: 497,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SignInPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n            lineNumber: 504,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n        lineNumber: 496,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SignInPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"SignInPageContent\");\n$RefreshReg$(_c1, \"SignInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/signin/page.tsx\n"));

/***/ })

});